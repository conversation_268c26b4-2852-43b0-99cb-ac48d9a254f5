-- Recovery Dashboard Seed Data
-- Created: 2024-12-29

-- Insert sample users
INSERT INTO users (email, name, role, permissions) VALUES
('<EMAIL>', 'Recovery Admin', 'admin', '{"all": true}'),
('<EMAIL>', 'Content Editor', 'editor', '{"content": true, "social": true}'),
('<EMAIL>', 'Virtual Assistant', 'va', '{"social": true, "shopify": true}');

-- Insert brands for different content types
INSERT INTO brands (name, type, description) VALUES
('Recovery Thoughts', 'serious', 'Thoughtful, inspirational recovery content focusing on hope and healing'),
('Dank Recovery Memes', 'humor', 'Humorous recovery content that brings lightness to the journey'),
('Spiritual Recovery', 'spiritual', 'Faith-based and spiritual aspects of recovery and personal growth'),
('Sound of Recovery', 'serious', 'Main brand for speaker tapes and educational content');

-- Insert sample recovery speakers
INSERT INTO speakers (name, program, audio_path, tags, status) VALUES
('<PERSON>', 'AA', '/audio/speakers/john_d_step4.mp3', '["step work", "inventory", "honesty"]', 'processed'),
('<PERSON>', 'NA', '/audio/speakers/sarah_m_sponsorship.mp3', '["sponsorship", "service", "helping others"]', 'processed'),
('Mike R.', 'AA', '/audio/speakers/mike_r_gratitude.mp3', '["gratitude", "daily practice", "mindfulness"]', 'pending'),
('Lisa K.', 'NA', '/audio/speakers/lisa_k_relapse.mp3', '["relapse", "recovery", "second chances"]', 'processed'),
('David P.', 'AA', '/audio/speakers/david_p_amends.mp3', '["amends", "step 9", "relationships"]', 'rendering'),
('Maria G.', 'AA', '/audio/speakers/maria_g_newcomer.mp3', '["newcomer", "first year", "basics"]', 'processed'),
('Tom W.', 'NA', '/audio/speakers/tom_w_longterm.mp3', '["long term recovery", "25 years", "wisdom"]', 'processed'),
('Jennifer L.', 'AA', '/audio/speakers/jennifer_l_women.mp3', '["women in recovery", "motherhood", "family"]', 'pending');

-- Insert sample videos (some uploaded to YouTube)
INSERT INTO videos (speaker_id, title, render_state, url, youtube_id, thumbnail_url, duration, views, likes, comments, uploaded_at) VALUES
(1, 'John D. - Step 4 Fearless Moral Inventory | AA Recovery Speaker | Sound of Recovery', 'uploaded', 'https://www.youtube.com/watch?v=abc123', 'abc123', 'https://img.youtube.com/vi/abc123/maxresdefault.jpg', 1800, 1250, 89, 23, NOW() - INTERVAL '5 days'),
(2, 'Sarah M. - The Gift of Sponsorship | NA Recovery Speaker | Sound of Recovery', 'uploaded', 'https://www.youtube.com/watch?v=def456', 'def456', 'https://img.youtube.com/vi/def456/maxresdefault.jpg', 2100, 890, 67, 18, NOW() - INTERVAL '3 days'),
(4, 'Lisa K. - Learning from Relapse | NA Recovery Speaker | Sound of Recovery', 'uploaded', 'https://www.youtube.com/watch?v=ghi789', 'ghi789', 'https://img.youtube.com/vi/ghi789/maxresdefault.jpg', 1650, 2100, 156, 42, NOW() - INTERVAL '1 day'),
(6, 'Maria G. - First Year Basics | AA Recovery Speaker | Sound of Recovery', 'completed', NULL, NULL, NULL, 1920, 0, 0, 0, NULL),
(7, 'Tom W. - 25 Years of Recovery Wisdom | NA Recovery Speaker | Sound of Recovery', 'completed', NULL, NULL, NULL, 2400, 0, 0, 0, NULL);

-- Insert sample social media posts
INSERT INTO posts (brand_id, platform, caption, asset_path, status, scheduled_at, posted_at) VALUES
(1, 'facebook', 'New speaker video: John D. shares his experience with Step 4. Link in bio! #Recovery #AA #StepWork', '/assets/posts/john_d_step4_fb.jpg', 'posted', NOW() - INTERVAL '5 days', NOW() - INTERVAL '5 days'),
(1, 'instagram', 'The courage to look within... 🙏 New video from John D. #Recovery #AA #Hope', '/assets/posts/john_d_step4_ig.jpg', 'posted', NOW() - INTERVAL '5 days', NOW() - INTERVAL '4 days'),
(2, 'instagram', 'When recovery gets real... 😂 #RecoveryMemes #SobrietyHumor #OneDay', '/assets/posts/meme_recovery_real.jpg', 'posted', NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 days'),
(3, 'facebook', 'Daily Reflection: "Grant me the serenity..." How has the Serenity Prayer helped your recovery? 🙏', '/assets/posts/serenity_prayer.jpg', 'scheduled', NOW() + INTERVAL '1 day', NULL),
(1, 'youtube', 'Sarah M. - The Gift of Sponsorship | NA Recovery Speaker', '/assets/videos/sarah_m_sponsorship.mp4', 'posted', NOW() - INTERVAL '3 days', NOW() - INTERVAL '3 days');

-- Insert sample products (recovery merchandise)
INSERT INTO products (shopify_id, title, price, inventory, status) VALUES
('prod_recovery_mug_001', 'One Day at a Time Coffee Mug', 19.99, 45, 'active'),
('prod_recovery_tshirt_001', 'Progress Not Perfection T-Shirt', 24.99, 23, 'active'),
('prod_recovery_sticker_001', 'Recovery Sticker Pack (5 pack)', 9.99, 150, 'active'),
('prod_recovery_hoodie_001', 'Serenity Prayer Hoodie', 39.99, 12, 'active'),
('prod_recovery_journal_001', 'Daily Gratitude Recovery Journal', 16.99, 8, 'active'),
('prod_recovery_keychain_001', 'AA/NA Medallion Keychain', 12.99, 67, 'active'),
('prod_recovery_book_001', 'Daily Reflections for Recovery', 14.99, 34, 'active');

-- Insert sample orders
INSERT INTO orders (shopify_id, buyer_email, buyer_name, total, status) VALUES
('order_001', '<EMAIL>', 'Michael Johnson', 44.98, 'delivered'),
('order_002', '<EMAIL>', 'Sarah Williams', 19.99, 'shipped'),
('order_003', '<EMAIL>', 'David Brown', 29.98, 'paid'),
('order_004', '<EMAIL>', 'Lisa Davis', 56.97, 'pending');

-- Insert order-product relationships
INSERT INTO order_products (order_id, product_id, quantity, price) VALUES
(1, 1, 1, 19.99), -- Michael: 1 mug
(1, 2, 1, 24.99), -- Michael: 1 t-shirt
(2, 1, 1, 19.99), -- Sarah: 1 mug
(3, 3, 3, 9.99),  -- David: 3 sticker packs
(4, 4, 1, 39.99), -- Lisa: 1 hoodie
(4, 5, 1, 16.99); -- Lisa: 1 journal

-- Insert sample books
INSERT INTO books (title, author, description) VALUES
('Daily Reflections for Recovery', 'Recovery Community', 'A collection of daily meditations and reflections for those in recovery'),
('The Big Book Study Guide', 'AA World Services', 'Comprehensive guide to studying the Big Book of Alcoholics Anonymous'),
('Living Clean: The Journey Continues', 'NA World Services', 'Narcotics Anonymous guide to living in recovery');

-- Insert sample daily readings
INSERT INTO readings (date, text, audio_url, book_id) VALUES
(CURRENT_DATE, 'Today I will focus on progress, not perfection. Recovery is a journey of small steps, each one bringing me closer to the person I want to become.', '/audio/readings/today.mp3', 1),
(CURRENT_DATE - INTERVAL '1 day', 'Gratitude transforms what we have into enough. Today I am grateful for my sobriety, my health, and the opportunity to help others.', '/audio/readings/yesterday.mp3', 1),
(CURRENT_DATE + INTERVAL '1 day', 'One day at a time. This simple phrase contains the wisdom of recovery. I cannot change the past or control the future, but I can live fully in this moment.', '/audio/readings/tomorrow.mp3', 1);

-- Link users to brands (many-to-many relationship)
INSERT INTO user_brands (user_id, brand_id) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), -- Admin has access to all brands
(2, 1), (2, 4), -- Editor has access to serious content
(3, 2), (3, 3); -- VA manages humor and spiritual content

-- Update sequences to ensure proper auto-increment
SELECT setval('users_id_seq', (SELECT MAX(id) FROM users));
SELECT setval('brands_id_seq', (SELECT MAX(id) FROM brands));
SELECT setval('speakers_id_seq', (SELECT MAX(id) FROM speakers));
SELECT setval('videos_id_seq', (SELECT MAX(id) FROM videos));
SELECT setval('posts_id_seq', (SELECT MAX(id) FROM posts));
SELECT setval('products_id_seq', (SELECT MAX(id) FROM products));
SELECT setval('orders_id_seq', (SELECT MAX(id) FROM orders));
SELECT setval('books_id_seq', (SELECT MAX(id) FROM books));
SELECT setval('readings_id_seq', (SELECT MAX(id) FROM readings));
