package services

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
)

// FacebookPage represents a Facebook page configuration
type FacebookPage struct {
	ID          string
	Name        string
	Token       string
	BrandType   string // "serious", "humor", "spiritual"
	Description string
}

// MultiPageFacebookService handles multiple Facebook pages
type MultiPageFacebookService struct {
	Pages map[string]*FacebookPage
}

// NewMultiPageFacebookService creates a service for managing multiple Facebook pages
func NewMultiPageFacebookService() *MultiPageFacebookService {
	return &MultiPageFacebookService{
		Pages: make(map[string]*FacebookPage),
	}
}

// AddPage adds a Facebook page to the service
func (mfs *MultiPageFacebookService) AddPage(id, name, token, brandType, description string) {
	mfs.Pages[id] = &FacebookPage{
		ID:          id,
		Name:        name,
		Token:       token,
		BrandType:   brandType,
		Description: description,
	}
}

// GetPage retrieves a specific page configuration
func (mfs *MultiPageFacebookService) GetPage(pageID string) (*FacebookPage, error) {
	page, exists := mfs.Pages[pageID]
	if !exists {
		return nil, fmt.Errorf("page with ID %s not found", pageID)
	}
	return page, nil
}

// GetAllPages returns all configured pages
func (mfs *MultiPageFacebookService) GetAllPages() map[string]*FacebookPage {
	return mfs.Pages
}

// ValidatePageToken checks if a specific page token is valid
func (mfs *MultiPageFacebookService) ValidatePageToken(pageID string) error {
	page, err := mfs.GetPage(pageID)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("https://graph.facebook.com/v18.0/%s?access_token=%s", pageID, page.Token)
	resp, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("failed to validate token for page %s: %w", pageID, err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("invalid token for page %s: %s", pageID, string(body))
	}

	return nil
}

// GetPageInfo retrieves information about a specific page
func (mfs *MultiPageFacebookService) GetPageInfo(pageID string) (*PageInfo, error) {
	page, err := mfs.GetPage(pageID)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("https://graph.facebook.com/v18.0/%s?fields=id,name,category,followers_count,fan_count,about,website,picture&access_token=%s",
		pageID, page.Token)

	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to get page info for %s: %w", pageID, err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Facebook API error for page %s: %s", pageID, string(body))
	}

	var pageInfo PageInfo
	if err := json.NewDecoder(resp.Body).Decode(&pageInfo); err != nil {
		return nil, fmt.Errorf("failed to decode page info for %s: %w", pageID, err)
	}

	return &pageInfo, nil
}

// CreatePost creates a post on a specific page with brand-appropriate content
func (mfs *MultiPageFacebookService) CreatePost(pageID string, req CreatePostRequest) (string, error) {
	page, err := mfs.GetPage(pageID)
	if err != nil {
		return "", err
	}

	postURL := fmt.Sprintf("https://graph.facebook.com/v18.0/%s/feed", pageID)

	// Prepare form data
	data := url.Values{}
	data.Set("message", req.Message)
	data.Set("access_token", page.Token)

	if req.Link != "" {
		data.Set("link", req.Link)
	}

	if req.ScheduledTime != "" {
		data.Set("scheduled_publish_time", req.ScheduledTime)
		data.Set("published", "false")
	} else {
		data.Set("published", "true")
	}

	resp, err := http.PostForm(postURL, data)
	if err != nil {
		return "", fmt.Errorf("failed to create post on page %s: %w", pageID, err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("Facebook API error for page %s: %s", pageID, string(body))
	}

	var response struct {
		ID string `json:"id"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response for page %s: %w", pageID, err)
	}

	return response.ID, nil
}

// CrossPostYouTubeVideo posts a YouTube video to a specific page with brand-appropriate messaging
func (mfs *MultiPageFacebookService) CrossPostYouTubeVideo(pageID, videoTitle, videoURL string, scheduleFor *time.Time) (string, error) {
	page, err := mfs.GetPage(pageID)
	if err != nil {
		return "", err
	}

	// Generate brand-appropriate message
	message := mfs.generateBrandMessage(page.BrandType, videoTitle)

	req := CreatePostRequest{
		Message: message,
		Link:    videoURL,
	}

	if scheduleFor != nil {
		req.ScheduledTime = fmt.Sprintf("%d", scheduleFor.Unix())
	}

	return mfs.CreatePost(pageID, req)
}

// generateBrandMessage creates appropriate messaging based on brand type
func (mfs *MultiPageFacebookService) generateBrandMessage(brandType, videoTitle string) string {
	switch brandType {
	case "humor":
		return fmt.Sprintf("New recovery content that hits different 😅 %s #RecoveryMemes #SobrietyHumor #Recovery", videoTitle)
	case "spiritual":
		return fmt.Sprintf("Blessed to share: %s 🙏 #Recovery #Faith #Hope #Spirituality", videoTitle)
	case "serious":
		fallthrough
	default:
		return fmt.Sprintf("New speaker video: %s 🙏 #Recovery #Hope #Healing #SoundOfRecovery", videoTitle)
	}
}

// GetPagePosts retrieves recent posts from a specific page
func (mfs *MultiPageFacebookService) GetPagePosts(pageID string, limit int) ([]PostData, error) {
	page, err := mfs.GetPage(pageID)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("https://graph.facebook.com/v18.0/%s/posts?fields=id,message,created_time,likes.summary(true),comments.summary(true),shares&limit=%d&access_token=%s",
		pageID, limit, page.Token)

	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to get posts for page %s: %w", pageID, err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Facebook API error for page %s: %s", pageID, string(body))
	}

	var response struct {
		Data []PostData `json:"data"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode posts for page %s: %w", pageID, err)
	}

	return response.Data, nil
}

// GetPageInsights retrieves analytics for a specific page
func (mfs *MultiPageFacebookService) GetPageInsights(pageID string) (*PageInsights, error) {
	page, err := mfs.GetPage(pageID)
	if err != nil {
		return nil, err
	}

	// Get insights for the last 30 days
	since := time.Now().AddDate(0, 0, -30).Unix()
	until := time.Now().Unix()

	url := fmt.Sprintf("https://graph.facebook.com/v18.0/%s/insights?metric=page_views_total,page_impressions,page_engaged_users,page_fan_adds&since=%d&until=%d&access_token=%s",
		pageID, since, until, page.Token)

	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to get insights for page %s: %w", pageID, err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Facebook API error for page %s: %s", pageID, string(body))
	}

	var response struct {
		Data []struct {
			Name   string `json:"name"`
			Values []struct {
				Value int `json:"value"`
			} `json:"values"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode insights for page %s: %w", pageID, err)
	}

	insights := &PageInsights{}
	for _, metric := range response.Data {
		if len(metric.Values) > 0 {
			switch metric.Name {
			case "page_views_total":
				insights.PageViews = metric.Values[0].Value
			case "page_impressions":
				insights.PageImpressions = metric.Values[0].Value
			case "page_engaged_users":
				insights.PageEngagement = metric.Values[0].Value
			case "page_fan_adds":
				insights.ReachTotal = metric.Values[0].Value
			}
		}
	}

	return insights, nil
}

// GetAllPagesStatus returns the status of all configured pages
func (mfs *MultiPageFacebookService) GetAllPagesStatus() map[string]interface{} {
	status := make(map[string]interface{})

	for pageID, page := range mfs.Pages {
		pageStatus := map[string]interface{}{
			"name":        page.Name,
			"brand_type":  page.BrandType,
			"description": page.Description,
			"configured":  page.Token != "",
		}

		// Test token validity
		if page.Token != "" {
			err := mfs.ValidatePageToken(pageID)
			pageStatus["token_valid"] = err == nil
			if err != nil {
				pageStatus["error"] = err.Error()
			}
		} else {
			pageStatus["token_valid"] = false
			pageStatus["error"] = "No token configured"
		}

		status[pageID] = pageStatus
	}

	return status
}
