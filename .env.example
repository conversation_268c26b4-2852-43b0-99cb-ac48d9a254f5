# Server Configuration
PORT=8080
ENVIRONMENT=development

# Database Configuration
DATABASE_TYPE=postgres
DATABASE_URL=postgres://recovery:password@localhost:5432/recovery_dashboard?sslmode=disable
# For SQLite (requires CGO):
# DATABASE_TYPE=sqlite
# DATABASE_URL=recovery.db

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Redis Configuration (for caching and queues)
REDIS_URL=redis://localhost:6379

# Shopify Integration
SHOPIFY_API_KEY=your-shopify-api-key
SHOPIFY_SECRET=your-shopify-secret
SHOPIFY_DOMAIN=your-store.myshopify.com

# S3-Compatible Storage (Cloudflare R2, AWS S3, etc.)
S3_BUCKET=your-bucket-name
S3_REGION=us-east-1
S3_ACCESS_KEY=your-access-key
S3_SECRET_KEY=your-secret-key
S3_ENDPOINT=https://your-endpoint.com  # Optional, for non-AWS S3

# Social Media API Keys
YOUTUBE_API_KEY=your-youtube-api-key
FACEBOOK_TOKEN=your-facebook-access-token
INSTAGRAM_TOKEN=your-instagram-access-token

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Webhook URLs (for external integrations)
SHOPIFY_WEBHOOK_SECRET=your-webhook-secret
YOUTUBE_WEBHOOK_URL=https://your-domain.com/webhooks/youtube

# Content Processing
FFMPEG_PATH=/usr/bin/ffmpeg
TEMP_DIR=/tmp/recovery-processing

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
