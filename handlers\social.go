package handlers

import (
	"net/http"
	"strconv"
	"time"

	"recovery-dashboard/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type SocialHandler struct {
	db *gorm.DB
}

func NewSocialHandler(db *gorm.DB) *SocialHandler {
	return &SocialHandler{db: db}
}

// Post handlers
func (h *SocialHandler) GetPosts(c *gin.Context) {
	var posts []models.Post
	
	query := h.db.Model(&models.Post{})
	
	// Add filters
	if status := c.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}
	if platform := c.Query("platform"); platform != "" {
		query = query.Where("platform = ?", platform)
	}
	if brandID := c.Query("brand_id"); brandID != "" {
		query = query.Where("brand_id = ?", brandID)
	}
	
	// Pagination
	page, _ := strconv.Atoi(c.<PERSON>ultQuer<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>("limit", "20"))
	offset := (page - 1) * limit
	
	if err := query.Preload("Brand").Offset(offset).Limit(limit).Order("created_at DESC").Find(&posts).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch posts"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"posts": posts,
		"page":  page,
		"limit": limit,
	})
}

func (h *SocialHandler) CreatePost(c *gin.Context) {
	var post models.Post
	
	if err := c.ShouldBindJSON(&post); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Set default status
	if post.Status == "" {
		post.Status = "draft"
	}
	
	if err := h.db.Create(&post).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create post"})
		return
	}
	
	// Load the brand relationship
	h.db.Preload("Brand").First(&post, post.ID)
	
	c.JSON(http.StatusCreated, gin.H{"post": post})
}

func (h *SocialHandler) GetPost(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid post ID"})
		return
	}
	
	var post models.Post
	if err := h.db.Preload("Brand").First(&post, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Post not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch post"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"post": post})
}

func (h *SocialHandler) UpdatePost(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid post ID"})
		return
	}
	
	var post models.Post
	if err := h.db.First(&post, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Post not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch post"})
		return
	}
	
	var updateData models.Post
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	if err := h.db.Model(&post).Updates(updateData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update post"})
		return
	}
	
	// Reload with brand
	h.db.Preload("Brand").First(&post, post.ID)
	
	c.JSON(http.StatusOK, gin.H{"post": post})
}

func (h *SocialHandler) DeletePost(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid post ID"})
		return
	}
	
	if err := h.db.Delete(&models.Post{}, uint(id)).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete post"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "Post deleted successfully"})
}

// Scheduling handlers
func (h *SocialHandler) SchedulePost(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid post ID"})
		return
	}
	
	var req struct {
		ScheduledAt string `json:"scheduled_at" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	scheduledAt, err := time.Parse(time.RFC3339, req.ScheduledAt)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid date format. Use RFC3339 format"})
		return
	}
	
	var post models.Post
	if err := h.db.First(&post, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Post not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch post"})
		return
	}
	
	// Update post status and scheduled time
	updates := map[string]interface{}{
		"status":       "scheduled",
		"scheduled_at": scheduledAt,
	}
	
	if err := h.db.Model(&post).Updates(updates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to schedule post"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"message":      "Post scheduled successfully",
		"scheduled_at": scheduledAt,
	})
}

func (h *SocialHandler) PublishPost(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid post ID"})
		return
	}
	
	var post models.Post
	if err := h.db.First(&post, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Post not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch post"})
		return
	}
	
	// TODO: Implement actual publishing logic for each platform
	// This would integrate with YouTube, Facebook, Instagram APIs
	
	now := time.Now()
	updates := map[string]interface{}{
		"status":    "posted",
		"posted_at": now,
	}
	
	if err := h.db.Model(&post).Updates(updates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update post status"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"message":   "Post published successfully",
		"posted_at": now,
	})
}

func (h *SocialHandler) GetScheduledPosts(c *gin.Context) {
	var posts []models.Post
	
	if err := h.db.Where("status = ?", "scheduled").
		Preload("Brand").
		Order("scheduled_at ASC").
		Find(&posts).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch scheduled posts"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"posts": posts})
}

// Brand handlers
func (h *SocialHandler) GetBrands(c *gin.Context) {
	var brands []models.Brand
	
	if err := h.db.Find(&brands).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch brands"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"brands": brands})
}

func (h *SocialHandler) CreateBrand(c *gin.Context) {
	var brand models.Brand
	
	if err := c.ShouldBindJSON(&brand); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	if err := h.db.Create(&brand).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create brand"})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{"brand": brand})
}

func (h *SocialHandler) UpdateBrand(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid brand ID"})
		return
	}
	
	var brand models.Brand
	if err := h.db.First(&brand, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Brand not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch brand"})
		return
	}
	
	var updateData models.Brand
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	if err := h.db.Model(&brand).Updates(updateData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update brand"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"brand": brand})
}
