package handlers

import (
	"net/http"
	"strconv"
	"time"

	"recovery-dashboard/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// FacebookHandler handles Facebook-related HTTP requests
type Facebook<PERSON>andler struct {
	db              *gorm.DB
	facebookService *services.FacebookService
}

// NewFacebookHandler creates a new Facebook handler
func NewFacebookHandler(db *gorm.DB, facebookService *services.FacebookService) *FacebookHandler {
	return &FacebookHandler{
		db:              db,
		facebookService: facebookService,
	}
}

// GetManagedPages retrieves all Facebook pages managed by the user
func (h *FacebookHandler) GetManagedPages(c *gin.Context) {
	pages, err := h.facebookService.GetManagedPages()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get managed pages: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"pages": pages})
}

// GetPageInfo retrieves information about a specific Facebook page
func (h *FacebookHandler) GetPageInfo(c *gin.Context) {
	pageID := c.Param("page_id")
	if pageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Page ID is required"})
		return
	}

	pageInfo, err := h.facebookService.GetPageInfo(pageID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get page info: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"page": pageInfo})
}

// GetPagePosts retrieves recent posts from a Facebook page
func (h *FacebookHandler) GetPagePosts(c *gin.Context) {
	pageID := c.Param("page_id")
	if pageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Page ID is required"})
		return
	}

	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 10
	}

	posts, err := h.facebookService.GetPagePosts(pageID, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get page posts: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"posts": posts, "count": len(posts)})
}

// CreatePost creates a new post on a Facebook page
func (h *FacebookHandler) CreatePost(c *gin.Context) {
	pageID := c.Param("page_id")
	if pageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Page ID is required"})
		return
	}

	var req struct {
		Message       string `json:"message" binding:"required"`
		Link          string `json:"link"`
		ScheduledTime string `json:"scheduled_time"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	postReq := services.CreatePostRequest{
		Message:       req.Message,
		Link:          req.Link,
		ScheduledTime: req.ScheduledTime,
	}

	postID, err := h.facebookService.CreatePost(pageID, postReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create post: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"post_id": postID,
		"message": "Post created successfully",
	})
}

// GetPageInsights retrieves analytics for a Facebook page
func (h *FacebookHandler) GetPageInsights(c *gin.Context) {
	pageID := c.Param("page_id")
	if pageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Page ID is required"})
		return
	}

	insights, err := h.facebookService.GetPageInsights(pageID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get page insights: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"insights": insights})
}

// ValidateToken checks if the Facebook access token is valid
func (h *FacebookHandler) ValidateToken(c *gin.Context) {
	err := h.facebookService.ValidateToken()
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"valid": false,
			"error": "Invalid Facebook token: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"valid":   true,
		"message": "Facebook token is valid",
	})
}

// CrossPostYouTubeVideo creates a Facebook post for a YouTube video
func (h *FacebookHandler) CrossPostYouTubeVideo(c *gin.Context) {
	pageID := c.Param("page_id")
	if pageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Page ID is required"})
		return
	}

	var req struct {
		VideoID     string `json:"video_id" binding:"required"`
		VideoTitle  string `json:"video_title" binding:"required"`
		VideoURL    string `json:"video_url" binding:"required"`
		Message     string `json:"message"`
		BrandType   string `json:"brand_type"` // "serious", "humor", "spiritual"
		ScheduleFor string `json:"schedule_for"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Generate appropriate message based on brand type
	message := req.Message
	if message == "" {
		switch req.BrandType {
		case "humor":
			message = "New recovery content that hits different 😅 " + req.VideoTitle + " #Recovery #SobrietyHumor"
		case "spiritual":
			message = "Blessed to share: " + req.VideoTitle + " 🙏 #Recovery #Faith #Hope"
		default:
			message = "New speaker video: " + req.VideoTitle + " 🙏 #Recovery #Hope #Healing"
		}
	}

	postReq := services.CreatePostRequest{
		Message:       message,
		Link:          req.VideoURL,
		ScheduledTime: req.ScheduleFor,
	}

	postID, err := h.facebookService.CreatePost(pageID, postReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cross-post video: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"post_id":    postID,
		"message":    "YouTube video cross-posted to Facebook successfully",
		"video_id":   req.VideoID,
		"facebook_post_id": postID,
	})
}

// GetPageAnalytics provides comprehensive analytics for a Facebook page
func (h *FacebookHandler) GetPageAnalytics(c *gin.Context) {
	pageID := c.Param("page_id")
	if pageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Page ID is required"})
		return
	}

	// Get page info and insights concurrently
	pageInfo, err := h.facebookService.GetPageInfo(pageID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get page info: " + err.Error()})
		return
	}

	insights, err := h.facebookService.GetPageInsights(pageID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get page insights: " + err.Error()})
		return
	}

	// Get recent posts for engagement analysis
	posts, err := h.facebookService.GetPagePosts(pageID, 10)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recent posts: " + err.Error()})
		return
	}

	// Calculate engagement metrics
	totalLikes := 0
	totalComments := 0
	totalShares := 0
	for _, post := range posts {
		totalLikes += post.Likes.Summary.TotalCount
		totalComments += post.Comments.Summary.TotalCount
		totalShares += post.Shares.Count
	}

	analytics := gin.H{
		"page_info": pageInfo,
		"insights":  insights,
		"engagement": gin.H{
			"total_likes":    totalLikes,
			"total_comments": totalComments,
			"total_shares":   totalShares,
			"recent_posts":   len(posts),
			"avg_engagement": float64(totalLikes+totalComments+totalShares) / float64(len(posts)),
		},
		"recent_posts": posts,
		"generated_at": time.Now(),
	}

	c.JSON(http.StatusOK, gin.H{"analytics": analytics})
}
