# Recovery Dashboard

A centralized, scalable, and maintainable Go-based dashboard to manage, automate, and track the performance of a multifaceted recovery-focused media and product ecosystem.

## 🚀 Features

### Content Pipeline
- **Speaker Management**: Track recovery speakers (AA/NA) with audio processing
- **Video Rendering**: Automated video creation and rendering pipeline
- **Content States**: Raw → Cleaned → Transcribed → Rendered → Published
- **Daily Readings**: Manage and distribute daily recovery readings

### E-commerce Integration
- **Shopify Integration**: Sync products, orders, and inventory
- **Order Management**: Track order status and fulfillment
- **Promotion Management**: Create and manage discount codes

### Social Media Automation
- **Multi-Platform Scheduling**: YouTube, Facebook, Instagram, TikTok
- **Brand Management**: Organize content by brand type (Humor, Serious, Spiritual)
- **Post States**: Draft → Scheduled → Posted
- **Asset Management**: Handle multimedia content

### Analytics & Metrics
- **Dashboard Overview**: Comprehensive metrics across all modules
- **Content Analytics**: Processing progress and video completion rates
- **Sales Metrics**: Revenue tracking and product performance
- **Social Metrics**: Post engagement and audience growth

## 🛠 Technology Stack

- **Backend**: Go with Gin framework
- **Database**: PostgreSQL (SQLite for development)
- **ORM**: GORM
- **Authentication**: JWT tokens
- **Caching**: Redis
- **Storage**: S3-compatible (Cloudflare R2)
- **Containerization**: Docker & Docker Compose

## 📁 Project Structure

```
recovery-dashboard/
├── cmd/                    # Application entry points
├── config/                 # Configuration management
├── handlers/               # HTTP request handlers
│   ├── content.go         # Content pipeline handlers
│   ├── shopify.go         # Shopify integration handlers
│   ├── social.go          # Social media handlers
│   ├── metrics.go         # Analytics handlers
│   └── user.go            # User management handlers
├── models/                 # Database models
├── routes/                 # Route definitions
├── services/               # Business logic services
├── utils/                  # Utility functions
├── migrations/             # Database migrations
├── static/                 # Static assets
├── templates/              # HTML templates
├── main.go                # Application entry point
├── Dockerfile             # Container configuration
├── docker-compose.yml     # Development environment
└── README.md              # This file
```

## 🚦 Getting Started

### Prerequisites

- Go 1.21 or higher
- Docker and Docker Compose (optional)
- PostgreSQL (if not using Docker)
- Redis (if not using Docker)

### Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd recovery-dashboard
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Install dependencies**
   ```bash
   go mod download
   ```

4. **Run with Docker Compose (Recommended)**
   ```bash
   docker-compose up -d
   ```

5. **Or run locally**
   ```bash
   # Make sure PostgreSQL and Redis are running
   go run main.go
   ```

6. **Access the application**
   - API: http://localhost:8080
   - Health Check: http://localhost:8080/health
   - Database Admin: http://localhost:8081 (if using Docker)

### API Documentation

The API follows RESTful conventions with the following base endpoints:

- **Authentication**: `/api/v1/auth/*`
- **Content Pipeline**: `/api/v1/content/*`
- **Shopify Integration**: `/api/v1/shopify/*`
- **Social Media**: `/api/v1/social/*`
- **Metrics**: `/api/v1/metrics/*`
- **User Management**: `/api/v1/users/*`

#### Example API Calls

```bash
# Health check
curl http://localhost:8080/health

# Get all speakers
curl http://localhost:8080/api/v1/content/speakers

# Create a new speaker
curl -X POST http://localhost:8080/api/v1/content/speakers \
  -H "Content-Type: application/json" \
  -d '{"name":"John D.","program":"AA","status":"pending"}'

# Get dashboard metrics
curl http://localhost:8080/api/v1/metrics/
```

## 🔧 Configuration

Key environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | `8080` |
| `ENVIRONMENT` | Environment (development/production) | `development` |
| `DATABASE_TYPE` | Database type (sqlite/postgres) | `sqlite` |
| `DATABASE_URL` | Database connection string | `recovery.db` |
| `JWT_SECRET` | JWT signing secret | Required |
| `SHOPIFY_API_KEY` | Shopify API key | Optional |
| `REDIS_URL` | Redis connection string | `redis://localhost:6379` |

## 🏗 Development Phases

### Phase 1: Core Infrastructure ✅
- [x] Project scaffold and structure
- [x] Database models and relationships
- [x] Basic API endpoints
- [x] Authentication framework
- [x] Docker configuration

### Phase 2: Content Pipeline (Next)
- [ ] Audio file processing
- [ ] Video rendering automation
- [ ] Transcription services
- [ ] Content workflow management

### Phase 3: Integrations
- [ ] Shopify API integration
- [ ] Social media platform APIs
- [ ] S3 storage integration
- [ ] Email notifications

### Phase 4: Advanced Features
- [ ] Scheduled job processing
- [ ] Advanced analytics
- [ ] User permissions system
- [ ] Frontend dashboard

## 🧪 Testing

```bash
# Run tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run specific test
go test ./handlers -v
```

## 📝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the API documentation
- Review the configuration examples
