package routes

import (
	"recovery-dashboard/config"
	"recovery-dashboard/handlers"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func SetupRoutes(router *gin.Engine, db *gorm.DB, cfg *config.Config) {
	// Initialize handlers (they will handle nil db gracefully)
	contentHandler := handlers.NewContentHandler(db)
	shopifyHandler := handlers.NewShopifyHandler(db, cfg)
	socialHandler := handlers.NewSocialHandler(db)
	metricsHandler := handlers.NewMetricsHandler(db)
	userHandler := handlers.NewUserHandler(db, cfg)

	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Authentication routes
		auth := v1.Group("/auth")
		{
			auth.POST("/login", userHandler.Login)
			auth.POST("/register", userHandler.Register)
			auth.POST("/refresh", userHandler.RefreshToken)
		}

		// Content Pipeline routes
		content := v1.Group("/content")
		{
			// Speakers
			content.GET("/speakers", contentHandler.GetSpeakers)
			content.POST("/speakers", contentHandler.CreateSpeaker)
			content.GET("/speakers/:id", contentHandler.GetSpeaker)
			content.PATCH("/speakers/:id", contentHandler.UpdateSpeaker)
			content.DELETE("/speakers/:id", contentHandler.DeleteSpeaker)

			// Videos
			content.GET("/videos", contentHandler.GetVideos)
			content.POST("/videos", contentHandler.CreateVideo)
			content.GET("/videos/:id", contentHandler.GetVideo)
			content.PATCH("/videos/:id", contentHandler.UpdateVideo)
			content.DELETE("/videos/:id", contentHandler.DeleteVideo)

			// Pipeline operations
			content.PATCH("/pipeline/:id", contentHandler.UpdatePipelineStatus)
			content.POST("/pipeline/:id/process", contentHandler.ProcessContent)
		}

		// Shopify Integration routes
		shopify := v1.Group("/shopify")
		{
			shopify.GET("/orders", shopifyHandler.GetOrders)
			shopify.GET("/orders/:id", shopifyHandler.GetOrder)
			shopify.POST("/orders/sync", shopifyHandler.SyncOrders)

			shopify.GET("/products", shopifyHandler.GetProducts)
			shopify.GET("/products/:id", shopifyHandler.GetProduct)
			shopify.POST("/products/sync", shopifyHandler.SyncProducts)

			shopify.POST("/promos", shopifyHandler.CreatePromotion)
			shopify.GET("/promos", shopifyHandler.GetPromotions)
		}

		// Social Media Scheduler routes
		social := v1.Group("/social")
		{
			// Posts
			social.GET("/posts", socialHandler.GetPosts)
			social.POST("/posts", socialHandler.CreatePost)
			social.GET("/posts/:id", socialHandler.GetPost)
			social.PATCH("/posts/:id", socialHandler.UpdatePost)
			social.DELETE("/posts/:id", socialHandler.DeletePost)

			// Scheduling
			social.POST("/posts/:id/schedule", socialHandler.SchedulePost)
			social.POST("/posts/:id/publish", socialHandler.PublishPost)
			social.GET("/posts/scheduled", socialHandler.GetScheduledPosts)

			// Brands
			social.GET("/brands", socialHandler.GetBrands)
			social.POST("/brands", socialHandler.CreateBrand)
			social.PATCH("/brands/:id", socialHandler.UpdateBrand)
		}

		// Metrics and Analytics routes
		metrics := v1.Group("/metrics")
		{
			metrics.GET("/", metricsHandler.GetDashboardMetrics)
			metrics.GET("/content", metricsHandler.GetContentMetrics)
			metrics.GET("/shopify", metricsHandler.GetShopifyMetrics)
			metrics.GET("/social", metricsHandler.GetSocialMetrics)
			metrics.GET("/audience", metricsHandler.GetAudienceMetrics)
		}

		// User Management routes
		users := v1.Group("/users")
		{
			users.GET("/", userHandler.GetUsers)
			users.POST("/", userHandler.CreateUser)
			users.GET("/:id", userHandler.GetUser)
			users.PATCH("/:id", userHandler.UpdateUser)
			users.DELETE("/:id", userHandler.DeleteUser)
			users.PATCH("/:id/permissions", userHandler.UpdatePermissions)
		}

		// Readings routes
		readings := v1.Group("/readings")
		{
			readings.GET("/", contentHandler.GetReadings)
			readings.POST("/", contentHandler.CreateReading)
			readings.GET("/:id", contentHandler.GetReading)
			readings.PATCH("/:id", contentHandler.UpdateReading)
			readings.GET("/today", contentHandler.GetTodayReading)
		}
	}

	// Serve static files (for frontend if using HTMX/AlpineJS)
	router.Static("/static", "./static")
	router.LoadHTMLGlob("templates/*")

	// Frontend routes (if using server-side rendering)
	router.GET("/", func(c *gin.Context) {
		c.HTML(200, "dashboard.html", gin.H{
			"title": "Recovery Dashboard",
		})
	})
}
