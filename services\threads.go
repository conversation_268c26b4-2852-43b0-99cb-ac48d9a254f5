package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// ThreadsService handles Threads API interactions
type ThreadsService struct {
	AccessToken string
	BaseURL     string
}

// NewThreadsService creates a new Threads service instance
func NewThreadsService(accessToken string) *ThreadsService {
	return &ThreadsService{
		AccessToken: accessToken,
		BaseURL:     "https://graph.threads.net",
	}
}

// ThreadsUser represents Threads user information
type ThreadsUser struct {
	ID        string `json:"id"`
	Username  string `json:"username"`
	Name      string `json:"name"`
	Bio       string `json:"biography"`
	Followers int    `json:"followers_count"`
	Following int    `json:"following_count"`
	Posts     int    `json:"media_count"`
}

// ThreadsPost represents a Threads post
type ThreadsPost struct {
	ID         string    `json:"id"`
	Text       string    `json:"text"`
	MediaURL   string    `json:"media_url,omitempty"`
	MediaType  string    `json:"media_type,omitempty"`
	Permalink  string    `json:"permalink"`
	Timestamp  time.Time `json:"timestamp"`
	LikeCount  int       `json:"like_count"`
	ReplyCount int       `json:"reply_count"`
}

// ThreadsAnalytics represents Threads analytics data
type ThreadsAnalytics struct {
	Impressions int `json:"impressions"`
	Reach       int `json:"reach"`
	Likes       int `json:"likes"`
	Replies     int `json:"replies"`
	Reposts     int `json:"reposts"`
	Quotes      int `json:"quotes"`
}

// ThreadsCreatePostRequest represents a request to create a Threads post
type ThreadsCreatePostRequest struct {
	Text      string `json:"text"`
	MediaURL  string `json:"media_url,omitempty"`
	MediaType string `json:"media_type,omitempty"`
	IsReply   bool   `json:"is_reply,omitempty"`
	ReplyTo   string `json:"reply_to,omitempty"`
}

// GetUserInfo retrieves Threads user information
func (ts *ThreadsService) GetUserInfo() (*ThreadsUser, error) {
	url := fmt.Sprintf("%s/v1.0/me?fields=id,username,name,biography,followers_count,following_count,media_count", ts.BaseURL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+ts.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Threads API error: %s", string(body))
	}

	var user ThreadsUser
	if err := json.NewDecoder(resp.Body).Decode(&user); err != nil {
		return nil, fmt.Errorf("failed to decode user info: %w", err)
	}

	return &user, nil
}

// CreatePost creates a Threads post
func (ts *ThreadsService) CreatePost(req ThreadsCreatePostRequest) (string, error) {
	url := fmt.Sprintf("%s/v1.0/me/threads", ts.BaseURL)

	jsonBody, err := json.Marshal(req)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Authorization", "Bearer "+ts.AccessToken)
	httpReq.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return "", fmt.Errorf("failed to create post: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("Threads API error: %s", string(body))
	}

	var response struct {
		ID string `json:"id"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	return response.ID, nil
}

// GetUserPosts retrieves user's Threads posts
func (ts *ThreadsService) GetUserPosts(limit int) ([]ThreadsPost, error) {
	url := fmt.Sprintf("%s/v1.0/me/threads?fields=id,text,media_url,media_type,permalink,timestamp,like_count,reply_count&limit=%d", ts.BaseURL, limit)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+ts.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get posts: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Threads API error: %s", string(body))
	}

	var response struct {
		Data []ThreadsPost `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode posts: %w", err)
	}

	return response.Data, nil
}

// GetAnalytics retrieves Threads analytics
func (ts *ThreadsService) GetAnalytics(days int) (*ThreadsAnalytics, error) {
	// Mock analytics for now since Threads API is new
	analytics := &ThreadsAnalytics{
		Impressions: 5200,
		Reach:       4100,
		Likes:       320,
		Replies:     45,
		Reposts:     28,
		Quotes:      12,
	}

	return analytics, nil
}

// ValidateToken checks if the Threads access token is valid
func (ts *ThreadsService) ValidateToken() error {
	_, err := ts.GetUserInfo()
	return err
}

// GenerateRecoveryPost creates Threads-appropriate content from YouTube videos
func (ts *ThreadsService) GenerateRecoveryPost(videoTitle, videoURL, accountType string) ThreadsCreatePostRequest {
	var text string

	switch accountType {
	case "merch":
		text = fmt.Sprintf("🔥 New recovery content + merch drop!\n\n%s\n\nCheck our latest designs and recovery resources 🙏\n\n#Recovery #RecoveryMerch #Sobriety #OneDayAtATime\n\n%s", videoTitle, videoURL)
	case "memes":
		text = fmt.Sprintf("Recovery mood today 😅\n\n%s\n\nWhen the program hits different 💯\n\n#RecoveryMemes #SoberLife #RecoveryHumor #AA #NA\n\n%s", videoTitle, videoURL)
	case "serious":
		text = fmt.Sprintf("Powerful recovery wisdom 🙏\n\n%s\n\nEvery story matters. Every day counts. Keep going.\n\n#Recovery #Hope #Healing #Inspiration #SoundOfRecovery\n\n%s", videoTitle, videoURL)
	default:
		text = fmt.Sprintf("New recovery content:\n\n%s\n\n#Recovery #Sobriety #Hope\n\n%s", videoTitle, videoURL)
	}

	return ThreadsCreatePostRequest{
		Text:      text,
		MediaType: "TEXT",
	}
}

// CrossPostYouTubeVideo creates a Threads post for a YouTube video
func (ts *ThreadsService) CrossPostYouTubeVideo(videoTitle, videoURL, accountType string) (string, error) {
	postContent := ts.GenerateRecoveryPost(videoTitle, videoURL, accountType)
	return ts.CreatePost(postContent)
}
