package services

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"recovery-dashboard/config"
	"recovery-dashboard/models"

	"google.golang.org/api/option"
	"google.golang.org/api/youtube/v3"
	"gorm.io/gorm"
)

type YouTubeService struct {
	service   *youtube.Service
	channelID string
	db        *gorm.DB
	cfg       *config.Config
}

type UploadRequest struct {
	VideoPath   string
	Title       string
	Description string
	Tags        []string
	CategoryID  string
	Privacy     string // "private", "unlisted", "public"
	SpeakerID   uint
}

type VideoAnalytics struct {
	VideoID      string    `json:"video_id"`
	Title        string    `json:"title"`
	Views        int64     `json:"views"`
	Likes        int64     `json:"likes"`
	Comments     int64     `json:"comments"`
	Duration     string    `json:"duration"`
	PublishedAt  time.Time `json:"published_at"`
	ThumbnailURL string    `json:"thumbnail_url"`
}

func NewYouTubeService(cfg *config.Config, db *gorm.DB) (*YouTubeService, error) {
	ctx := context.Background()

	// Initialize YouTube service with API key
	service, err := youtube.NewService(ctx, option.WithAPIKey(cfg.YouTubeAPIKey))
	if err != nil {
		return nil, fmt.Errorf("failed to create YouTube service: %w", err)
	}

	return &YouTubeService{
		service:   service,
		channelID: cfg.YouTubeChannelID,
		db:        db,
		cfg:       cfg,
	}, nil
}

// UploadVideo uploads a video to YouTube and updates the database
func (ys *YouTubeService) UploadVideo(ctx context.Context, req UploadRequest) (*youtube.Video, error) {
	// Open video file
	file, err := os.Open(req.VideoPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open video file: %w", err)
	}
	defer file.Close()

	// Create video snippet
	video := &youtube.Video{
		Snippet: &youtube.VideoSnippet{
			Title:       req.Title,
			Description: req.Description,
			Tags:        req.Tags,
			CategoryId:  req.CategoryID,
			ChannelId:   ys.channelID,
		},
		Status: &youtube.VideoStatus{
			PrivacyStatus: req.Privacy,
		},
	}

	// Create upload call
	call := ys.service.Videos.Insert([]string{"snippet", "status"}, video)

	// Set the media upload
	call = call.Media(file)

	// Execute upload
	response, err := call.Do()
	if err != nil {
		return nil, fmt.Errorf("failed to upload video: %w", err)
	}

	// Update database if speaker ID provided
	if req.SpeakerID > 0 && ys.db != nil {
		videoRecord := models.Video{
			SpeakerID:   req.SpeakerID,
			Title:       req.Title,
			RenderState: "uploaded",
			URL:         fmt.Sprintf("https://www.youtube.com/watch?v=%s", response.Id),
		}

		if err := ys.db.Create(&videoRecord).Error; err != nil {
			log.Printf("Failed to save video to database: %v", err)
		}
	}

	log.Printf("Video uploaded successfully: %s (ID: %s)", req.Title, response.Id)
	return response, nil
}

// GetChannelVideos retrieves videos from the Sound of Recovery channel
func (ys *YouTubeService) GetChannelVideos(ctx context.Context, maxResults int64) ([]*youtube.SearchResult, error) {
	call := ys.service.Search.List([]string{"snippet"}).
		ChannelId(ys.channelID).
		Type("video").
		Order("date").
		MaxResults(maxResults)

	response, err := call.Do()
	if err != nil {
		return nil, fmt.Errorf("failed to get channel videos: %w", err)
	}

	return response.Items, nil
}

// GetVideoAnalytics retrieves analytics for a specific video
func (ys *YouTubeService) GetVideoAnalytics(ctx context.Context, videoID string) (*VideoAnalytics, error) {
	// Get video details
	call := ys.service.Videos.List([]string{"snippet", "statistics", "contentDetails"}).
		Id(videoID)

	response, err := call.Do()
	if err != nil {
		return nil, fmt.Errorf("failed to get video details: %w", err)
	}

	if len(response.Items) == 0 {
		return nil, fmt.Errorf("video not found: %s", videoID)
	}

	video := response.Items[0]

	// Parse published date
	publishedAt, _ := time.Parse(time.RFC3339, video.Snippet.PublishedAt)

	analytics := &VideoAnalytics{
		VideoID:      videoID,
		Title:        video.Snippet.Title,
		Views:        int64(video.Statistics.ViewCount),
		Likes:        int64(video.Statistics.LikeCount),
		Comments:     int64(video.Statistics.CommentCount),
		Duration:     video.ContentDetails.Duration,
		PublishedAt:  publishedAt,
		ThumbnailURL: video.Snippet.Thumbnails.Medium.Url,
	}

	return analytics, nil
}

// GetChannelAnalytics retrieves overall channel analytics
func (ys *YouTubeService) GetChannelAnalytics(ctx context.Context) (map[string]interface{}, error) {
	// Get channel details
	call := ys.service.Channels.List([]string{"snippet", "statistics"}).
		Id(ys.channelID)

	response, err := call.Do()
	if err != nil {
		return nil, fmt.Errorf("failed to get channel details: %w", err)
	}

	if len(response.Items) == 0 {
		return nil, fmt.Errorf("channel not found: %s", ys.channelID)
	}

	channel := response.Items[0]

	analytics := map[string]interface{}{
		"channel_id":       ys.channelID,
		"channel_title":    channel.Snippet.Title,
		"subscriber_count": channel.Statistics.SubscriberCount,
		"video_count":      channel.Statistics.VideoCount,
		"view_count":       channel.Statistics.ViewCount,
		"description":      channel.Snippet.Description,
		"published_at":     channel.Snippet.PublishedAt,
		"thumbnail_url":    channel.Snippet.Thumbnails.Medium.Url,
	}

	return analytics, nil
}

// CreatePlaylist creates a new playlist for organizing speaker content
func (ys *YouTubeService) CreatePlaylist(ctx context.Context, title, description string) (*youtube.Playlist, error) {
	playlist := &youtube.Playlist{
		Snippet: &youtube.PlaylistSnippet{
			Title:       title,
			Description: description,
		},
		Status: &youtube.PlaylistStatus{
			PrivacyStatus: "public",
		},
	}

	call := ys.service.Playlists.Insert([]string{"snippet", "status"}, playlist)
	response, err := call.Do()
	if err != nil {
		return nil, fmt.Errorf("failed to create playlist: %w", err)
	}

	log.Printf("Playlist created: %s (ID: %s)", title, response.Id)
	return response, nil
}

// AddVideoToPlaylist adds a video to a specific playlist
func (ys *YouTubeService) AddVideoToPlaylist(ctx context.Context, playlistID, videoID string) error {
	playlistItem := &youtube.PlaylistItem{
		Snippet: &youtube.PlaylistItemSnippet{
			PlaylistId: playlistID,
			ResourceId: &youtube.ResourceId{
				Kind:    "youtube#video",
				VideoId: videoID,
			},
		},
	}

	call := ys.service.PlaylistItems.Insert([]string{"snippet"}, playlistItem)
	_, err := call.Do()
	if err != nil {
		return fmt.Errorf("failed to add video to playlist: %w", err)
	}

	return nil
}

// GenerateVideoTitle creates a standardized title for speaker videos
func (ys *YouTubeService) GenerateVideoTitle(speaker models.Speaker, topic string) string {
	if topic != "" {
		return fmt.Sprintf("%s - %s | %s Recovery Speaker | Sound of Recovery",
			speaker.Name, topic, speaker.Program)
	}
	return fmt.Sprintf("%s | %s Recovery Speaker | Sound of Recovery",
		speaker.Name, speaker.Program)
}

// GenerateVideoDescription creates a standardized description for speaker videos
func (ys *YouTubeService) GenerateVideoDescription(speaker models.Speaker, topic string) string {
	var description strings.Builder

	description.WriteString(fmt.Sprintf("🎙️ %s Recovery Speaker: %s\n\n", speaker.Program, speaker.Name))

	if topic != "" {
		description.WriteString(fmt.Sprintf("📝 Topic: %s\n\n", topic))
	}

	description.WriteString("Welcome to Sound of Recovery, where we share powerful stories of hope, healing, and transformation from the recovery community.\n\n")
	description.WriteString("🔔 Subscribe for more inspiring recovery content\n")
	description.WriteString("💬 Share your thoughts in the comments\n")
	description.WriteString("🙏 Remember: You are not alone in your journey\n\n")
	description.WriteString("#Recovery #SobrietyJourney #AddictionRecovery #Hope #Healing")

	if speaker.Program == "AA" {
		description.WriteString(" #AlcoholicsAnonymous #AA")
	} else if speaker.Program == "NA" {
		description.WriteString(" #NarcoticsAnonymous #NA")
	}

	return description.String()
}

// GetVideoTags generates relevant tags for speaker videos
func (ys *YouTubeService) GetVideoTags(speaker models.Speaker, topic string) []string {
	tags := []string{
		"recovery",
		"sobriety",
		"addiction recovery",
		"sound of recovery",
		speaker.Program,
		"recovery speaker",
		"hope",
		"healing",
		"transformation",
	}

	if speaker.Program == "AA" {
		tags = append(tags, "alcoholics anonymous", "alcohol recovery")
	} else if speaker.Program == "NA" {
		tags = append(tags, "narcotics anonymous", "drug recovery")
	}

	if topic != "" {
		// Add topic-specific tags
		topicWords := strings.Fields(strings.ToLower(topic))
		tags = append(tags, topicWords...)
	}

	return tags
}
