package config

import (
	"fmt"
	"os"
	"strconv"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"github.com/glebarez/sqlite"
)

type Config struct {
	Environment         string
	DatabaseURL         string
	DatabaseType        string
	JWTSecret           string
	RedisURL            string
	ShopifyAPIKey       string
	ShopifySecret       string
	ShopifyDomain       string
	S3Bucket            string
	S3Region            string
	S3AccessKey         string
	S3SecretKey         string
	YouTubeAPIKey       string
	YouTubeClientID     string
	YouTubeClientSecret string
	YouTubeChannelID    string

	// Facebook Integration - Multi-Page Support
	FacebookToken                string // Default token for backward compatibility
	FacebookSoundOfRecoveryToken string
	FacebookSoundOfRecoveryID    string
	FacebookRecoveryMemesToken   string
	FacebookRecoveryMemesID      string

	// TikTok Integration - Multi-Account Support
	TikTokRecoveryMerchToken string
	TikTokRecoveryMemesToken string
	TikTokSeriousToken       string

	InstagramToken string
	Port           int
}

func Load() *Config {
	port, _ := strconv.Atoi(getEnv("PORT", "8080"))

	return &Config{
		Environment:         getEnv("ENVIRONMENT", "development"),
		DatabaseURL:         getEnv("DATABASE_URL", "recovery.db"),
		DatabaseType:        getEnv("DATABASE_TYPE", "sqlite"),
		JWTSecret:           getEnv("JWT_SECRET", "your-secret-key"),
		RedisURL:            getEnv("REDIS_URL", "redis://localhost:6379"),
		ShopifyAPIKey:       getEnv("SHOPIFY_API_KEY", ""),
		ShopifySecret:       getEnv("SHOPIFY_SECRET", ""),
		ShopifyDomain:       getEnv("SHOPIFY_DOMAIN", ""),
		S3Bucket:            getEnv("S3_BUCKET", ""),
		S3Region:            getEnv("S3_REGION", "us-east-1"),
		S3AccessKey:         getEnv("S3_ACCESS_KEY", ""),
		S3SecretKey:         getEnv("S3_SECRET_KEY", ""),
		YouTubeAPIKey:       getEnv("YOUTUBE_API_KEY", ""),
		YouTubeClientID:     getEnv("YOUTUBE_CLIENT_ID", ""),
		YouTubeClientSecret: getEnv("YOUTUBE_CLIENT_SECRET", ""),
		YouTubeChannelID:    getEnv("YOUTUBE_CHANNEL_ID", ""),

		// Facebook Integration - Multi-Page Support
		FacebookToken:                getEnv("FACEBOOK_TOKEN", ""),
		FacebookSoundOfRecoveryToken: getEnv("FACEBOOK_SOUND_OF_RECOVERY_TOKEN", ""),
		FacebookSoundOfRecoveryID:    getEnv("FACEBOOK_SOUND_OF_RECOVERY_ID", ""),
		FacebookRecoveryMemesToken:   getEnv("FACEBOOK_RECOVERY_MEMES_TOKEN", ""),
		FacebookRecoveryMemesID:      getEnv("FACEBOOK_RECOVERY_MEMES_ID", ""),

		// TikTok Integration - Multi-Account Support
		TikTokRecoveryMerchToken: getEnv("TIKTOK_RECOVERY_MERCH_TOKEN", ""),
		TikTokRecoveryMemesToken: getEnv("TIKTOK_RECOVERY_MEMES_TOKEN", ""),
		TikTokSeriousToken:       getEnv("TIKTOK_SERIOUS_TOKEN", ""),

		InstagramToken: getEnv("INSTAGRAM_TOKEN", ""),
		Port:           port,
	}
}

func InitDB(cfg *Config) (*gorm.DB, error) {
	var db *gorm.DB
	var err error

	config := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}

	switch cfg.DatabaseType {
	case "postgres":
		db, err = gorm.Open(postgres.Open(cfg.DatabaseURL), config)
	case "sqlite":
		// Use pure Go SQLite driver (no CGO required)
		db, err = gorm.Open(sqlite.Open(cfg.DatabaseURL), config)
	default:
		return nil, fmt.Errorf("unsupported database type: %s", cfg.DatabaseType)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	return db, nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
