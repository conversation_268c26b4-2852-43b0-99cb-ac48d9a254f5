package services

import (
	"log"
	"time"

	"recovery-dashboard/models"

	"gorm.io/gorm"
)

// SeedDatabase populates the database with sample data for testing
func SeedDatabase(db *gorm.DB) error {
	log.Println("Seeding database with sample data...")

	// Check if data already exists
	var userCount int64
	db.Model(&models.User{}).Count(&userCount)
	if userCount > 0 {
		log.Println("Database already contains data, skipping seed")
		return nil
	}

	// Seed Users
	users := []models.User{
		{
			Email:       "<EMAIL>",
			Name:        "Recovery Admin",
			Role:        "admin",
			Permissions: `{"all": true}`,
		},
		{
			Email:       "<EMAIL>",
			Name:        "Content Editor",
			Role:        "editor",
			Permissions: `{"content": true, "social": true}`,
		},
		{
			Email:       "<EMAIL>",
			Name:        "Virtual Assistant",
			Role:        "va",
			Permissions: `{"social": true, "shopify": true}`,
		},
	}

	for _, user := range users {
		if err := db.Create(&user).Error; err != nil {
			return err
		}
	}

	// Seed Brands
	brands := []models.Brand{
		{
			Name:        "Recovery Thoughts",
			Type:        "serious",
			Description: "Thoughtful, inspirational recovery content focusing on hope and healing",
		},
		{
			Name:        "Dank Recovery Memes",
			Type:        "humor",
			Description: "Humorous recovery content that brings lightness to the journey",
		},
		{
			Name:        "Spiritual Recovery",
			Type:        "spiritual",
			Description: "Faith-based and spiritual aspects of recovery and personal growth",
		},
		{
			Name:        "Sound of Recovery",
			Type:        "serious",
			Description: "Main brand for speaker tapes and educational content",
		},
	}

	for _, brand := range brands {
		if err := db.Create(&brand).Error; err != nil {
			return err
		}
	}

	// Seed Speakers
	speakers := []models.Speaker{
		{
			Name:      "John D.",
			Program:   "AA",
			AudioPath: "/audio/speakers/john_d_step4.mp3",
			Tags:      `["step work", "inventory", "honesty"]`,
			Status:    "processed",
		},
		{
			Name:      "Sarah M.",
			Program:   "NA",
			AudioPath: "/audio/speakers/sarah_m_sponsorship.mp3",
			Tags:      `["sponsorship", "service", "helping others"]`,
			Status:    "processed",
		},
		{
			Name:      "Mike R.",
			Program:   "AA",
			AudioPath: "/audio/speakers/mike_r_gratitude.mp3",
			Tags:      `["gratitude", "daily practice", "mindfulness"]`,
			Status:    "pending",
		},
		{
			Name:      "Lisa K.",
			Program:   "NA",
			AudioPath: "/audio/speakers/lisa_k_relapse.mp3",
			Tags:      `["relapse", "recovery", "second chances"]`,
			Status:    "processed",
		},
		{
			Name:      "David P.",
			Program:   "AA",
			AudioPath: "/audio/speakers/david_p_amends.mp3",
			Tags:      `["amends", "step 9", "relationships"]`,
			Status:    "rendering",
		},
		{
			Name:      "Maria G.",
			Program:   "AA",
			AudioPath: "/audio/speakers/maria_g_newcomer.mp3",
			Tags:      `["newcomer", "first year", "basics"]`,
			Status:    "processed",
		},
		{
			Name:      "Tom W.",
			Program:   "NA",
			AudioPath: "/audio/speakers/tom_w_longterm.mp3",
			Tags:      `["long term recovery", "25 years", "wisdom"]`,
			Status:    "processed",
		},
		{
			Name:      "Jennifer L.",
			Program:   "AA",
			AudioPath: "/audio/speakers/jennifer_l_women.mp3",
			Tags:      `["women in recovery", "motherhood", "family"]`,
			Status:    "pending",
		},
	}

	for _, speaker := range speakers {
		if err := db.Create(&speaker).Error; err != nil {
			return err
		}
	}

	// Seed Videos (some uploaded to YouTube)
	now := time.Now()
	uploadedAt1 := now.AddDate(0, 0, -5)
	uploadedAt2 := now.AddDate(0, 0, -3)
	uploadedAt3 := now.AddDate(0, 0, -1)

	videos := []models.Video{
		{
			SpeakerID:    1,
			Title:        "John D. - Step 4 Fearless Moral Inventory | AA Recovery Speaker | Sound of Recovery",
			RenderState:  "uploaded",
			URL:          "https://www.youtube.com/watch?v=abc123",
			YouTubeID:    "abc123",
			ThumbnailURL: "https://img.youtube.com/vi/abc123/maxresdefault.jpg",
			Duration:     1800,
			Views:        1250,
			Likes:        89,
			Comments:     23,
			UploadedAt:   &uploadedAt1,
		},
		{
			SpeakerID:    2,
			Title:        "Sarah M. - The Gift of Sponsorship | NA Recovery Speaker | Sound of Recovery",
			RenderState:  "uploaded",
			URL:          "https://www.youtube.com/watch?v=def456",
			YouTubeID:    "def456",
			ThumbnailURL: "https://img.youtube.com/vi/def456/maxresdefault.jpg",
			Duration:     2100,
			Views:        890,
			Likes:        67,
			Comments:     18,
			UploadedAt:   &uploadedAt2,
		},
		{
			SpeakerID:    4,
			Title:        "Lisa K. - Learning from Relapse | NA Recovery Speaker | Sound of Recovery",
			RenderState:  "uploaded",
			URL:          "https://www.youtube.com/watch?v=ghi789",
			YouTubeID:    "ghi789",
			ThumbnailURL: "https://img.youtube.com/vi/ghi789/maxresdefault.jpg",
			Duration:     1650,
			Views:        2100,
			Likes:        156,
			Comments:     42,
			UploadedAt:   &uploadedAt3,
		},
		{
			SpeakerID:   6,
			Title:       "Maria G. - First Year Basics | AA Recovery Speaker | Sound of Recovery",
			RenderState: "completed",
			Duration:    1920,
		},
		{
			SpeakerID:   7,
			Title:       "Tom W. - 25 Years of Recovery Wisdom | NA Recovery Speaker | Sound of Recovery",
			RenderState: "completed",
			Duration:    2400,
		},
	}

	for _, video := range videos {
		if err := db.Create(&video).Error; err != nil {
			return err
		}
	}

	// Seed Posts
	scheduledAt := now.AddDate(0, 0, 1)
	postedAt1 := now.AddDate(0, 0, -5)
	postedAt2 := now.AddDate(0, 0, -4)
	postedAt3 := now.AddDate(0, 0, -2)
	postedAt4 := now.AddDate(0, 0, -3)

	posts := []models.Post{
		{
			BrandID:   1,
			Platform:  "facebook",
			Caption:   "New speaker video: John D. shares his experience with Step 4. Link in bio! #Recovery #AA #StepWork",
			AssetPath: "/assets/posts/john_d_step4_fb.jpg",
			Status:    "posted",
			PostedAt:  &postedAt1,
		},
		{
			BrandID:   1,
			Platform:  "instagram",
			Caption:   "The courage to look within... 🙏 New video from John D. #Recovery #AA #Hope",
			AssetPath: "/assets/posts/john_d_step4_ig.jpg",
			Status:    "posted",
			PostedAt:  &postedAt2,
		},
		{
			BrandID:   2,
			Platform:  "instagram",
			Caption:   "When recovery gets real... 😂 #RecoveryMemes #SobrietyHumor #OneDay",
			AssetPath: "/assets/posts/meme_recovery_real.jpg",
			Status:    "posted",
			PostedAt:  &postedAt3,
		},
		{
			BrandID:     3,
			Platform:    "facebook",
			Caption:     "Daily Reflection: \"Grant me the serenity...\" How has the Serenity Prayer helped your recovery? 🙏",
			AssetPath:   "/assets/posts/serenity_prayer.jpg",
			Status:      "scheduled",
			ScheduledAt: &scheduledAt,
		},
		{
			BrandID:   1,
			Platform:  "youtube",
			Caption:   "Sarah M. - The Gift of Sponsorship | NA Recovery Speaker",
			AssetPath: "/assets/videos/sarah_m_sponsorship.mp4",
			Status:    "posted",
			PostedAt:  &postedAt4,
		},
	}

	for _, post := range posts {
		if err := db.Create(&post).Error; err != nil {
			return err
		}
	}

	// Seed Products
	products := []models.Product{
		{ShopifyID: "prod_recovery_mug_001", Title: "One Day at a Time Coffee Mug", Price: 19.99, Inventory: 45, Status: "active"},
		{ShopifyID: "prod_recovery_tshirt_001", Title: "Progress Not Perfection T-Shirt", Price: 24.99, Inventory: 23, Status: "active"},
		{ShopifyID: "prod_recovery_sticker_001", Title: "Recovery Sticker Pack (5 pack)", Price: 9.99, Inventory: 150, Status: "active"},
		{ShopifyID: "prod_recovery_hoodie_001", Title: "Serenity Prayer Hoodie", Price: 39.99, Inventory: 12, Status: "active"},
		{ShopifyID: "prod_recovery_journal_001", Title: "Daily Gratitude Recovery Journal", Price: 16.99, Inventory: 8, Status: "active"},
		{ShopifyID: "prod_recovery_keychain_001", Title: "AA/NA Medallion Keychain", Price: 12.99, Inventory: 67, Status: "active"},
		{ShopifyID: "prod_recovery_book_001", Title: "Daily Reflections for Recovery", Price: 14.99, Inventory: 34, Status: "active"},
	}

	for _, product := range products {
		if err := db.Create(&product).Error; err != nil {
			return err
		}
	}

	// Seed Orders
	orders := []models.Order{
		{ShopifyID: "order_001", BuyerEmail: "<EMAIL>", BuyerName: "Michael Johnson", Total: 44.98, Status: "delivered"},
		{ShopifyID: "order_002", BuyerEmail: "<EMAIL>", BuyerName: "Sarah Williams", Total: 19.99, Status: "shipped"},
		{ShopifyID: "order_003", BuyerEmail: "<EMAIL>", BuyerName: "David Brown", Total: 29.98, Status: "paid"},
		{ShopifyID: "order_004", BuyerEmail: "<EMAIL>", BuyerName: "Lisa Davis", Total: 56.97, Status: "pending"},
	}

	for _, order := range orders {
		if err := db.Create(&order).Error; err != nil {
			return err
		}
	}

	// Seed Books
	books := []models.Book{
		{Title: "Daily Reflections for Recovery", Author: "Recovery Community", Description: "A collection of daily meditations and reflections for those in recovery"},
		{Title: "The Big Book Study Guide", Author: "AA World Services", Description: "Comprehensive guide to studying the Big Book of Alcoholics Anonymous"},
		{Title: "Living Clean: The Journey Continues", Author: "NA World Services", Description: "Narcotics Anonymous guide to living in recovery"},
	}

	for _, book := range books {
		if err := db.Create(&book).Error; err != nil {
			return err
		}
	}

	// Seed Readings
	today := time.Now().Truncate(24 * time.Hour)
	readings := []models.Reading{
		{
			Date:     today,
			Text:     "Today I will focus on progress, not perfection. Recovery is a journey of small steps, each one bringing me closer to the person I want to become.",
			AudioURL: "/audio/readings/today.mp3",
			BookID:   1,
		},
		{
			Date:     today.AddDate(0, 0, -1),
			Text:     "Gratitude transforms what we have into enough. Today I am grateful for my sobriety, my health, and the opportunity to help others.",
			AudioURL: "/audio/readings/yesterday.mp3",
			BookID:   1,
		},
		{
			Date:     today.AddDate(0, 0, 1),
			Text:     "One day at a time. This simple phrase contains the wisdom of recovery. I cannot change the past or control the future, but I can live fully in this moment.",
			AudioURL: "/audio/readings/tomorrow.mp3",
			BookID:   1,
		},
	}

	for _, reading := range readings {
		if err := db.Create(&reading).Error; err != nil {
			return err
		}
	}

	log.Println("Database seeded successfully!")
	return nil
}
