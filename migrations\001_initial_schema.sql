-- Recovery Dashboard Initial Schema
-- Created: 2024-12-29

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'editor',
    permissions TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Brands table
CREATE TABLE brands (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    type VARCHAR(50) NOT NULL, -- humor, serious, spiritual
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Speakers table
CREATE TABLE speakers (
    id SERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    program VARCHAR(10) NOT NULL, -- AA, NA, etc.
    audio_path VARCHAR(500),
    tags TEXT, -- JSON array of tags
    status VARCHAR(50) DEFAULT 'pending', -- pending, processed, published
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Videos table
CREATE TABLE videos (
    id SERIAL PRIMARY KEY,
    speaker_id INTEGER NOT NULL REFERENCES speakers(id),
    title VARCHAR(500) NOT NULL,
    render_state VARCHAR(50) DEFAULT 'pending', -- pending, rendering, completed, uploaded, failed
    url VARCHAR(500),
    youtube_id VARCHAR(100),
    thumbnail_url VARCHAR(500),
    duration INTEGER, -- in seconds
    views BIGINT DEFAULT 0,
    likes BIGINT DEFAULT 0,
    comments BIGINT DEFAULT 0,
    uploaded_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Posts table
CREATE TABLE posts (
    id SERIAL PRIMARY KEY,
    brand_id INTEGER NOT NULL REFERENCES brands(id),
    platform VARCHAR(50) NOT NULL, -- youtube, facebook, instagram, tiktok
    caption TEXT,
    asset_path VARCHAR(500),
    status VARCHAR(50) DEFAULT 'draft', -- draft, scheduled, posted, failed
    scheduled_at TIMESTAMP WITH TIME ZONE,
    posted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Products table
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    shopify_id VARCHAR(100) UNIQUE NOT NULL,
    title VARCHAR(500) NOT NULL,
    price DECIMAL(10,2),
    inventory INTEGER,
    status VARCHAR(50) DEFAULT 'active', -- active, inactive, archived
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Orders table
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    shopify_id VARCHAR(100) UNIQUE NOT NULL,
    buyer_email VARCHAR(255),
    buyer_name VARCHAR(255),
    total DECIMAL(10,2),
    status VARCHAR(50) NOT NULL, -- pending, paid, shipped, delivered, cancelled
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Books table
CREATE TABLE books (
    id SERIAL PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    author VARCHAR(255),
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Readings table
CREATE TABLE readings (
    id SERIAL PRIMARY KEY,
    date DATE UNIQUE NOT NULL,
    text TEXT NOT NULL,
    audio_url VARCHAR(500),
    book_id INTEGER REFERENCES books(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Junction tables for many-to-many relationships

-- User-Brand relationships
CREATE TABLE user_brands (
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    brand_id INTEGER REFERENCES brands(id) ON DELETE CASCADE,
    PRIMARY KEY (user_id, brand_id)
);

-- Order-Product relationships
CREATE TABLE order_products (
    order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
    product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER DEFAULT 1,
    price DECIMAL(10,2),
    PRIMARY KEY (order_id, product_id)
);

-- Indexes for performance
CREATE INDEX idx_speakers_program ON speakers(program);
CREATE INDEX idx_speakers_status ON speakers(status);
CREATE INDEX idx_videos_speaker_id ON videos(speaker_id);
CREATE INDEX idx_videos_render_state ON videos(render_state);
CREATE INDEX idx_videos_youtube_id ON videos(youtube_id);
CREATE INDEX idx_posts_brand_id ON posts(brand_id);
CREATE INDEX idx_posts_platform ON posts(platform);
CREATE INDEX idx_posts_status ON posts(status);
CREATE INDEX idx_posts_scheduled_at ON posts(scheduled_at);
CREATE INDEX idx_products_shopify_id ON products(shopify_id);
CREATE INDEX idx_orders_shopify_id ON orders(shopify_id);
CREATE INDEX idx_readings_date ON readings(date);

-- Updated at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to all tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_brands_updated_at BEFORE UPDATE ON brands FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_speakers_updated_at BEFORE UPDATE ON speakers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_videos_updated_at BEFORE UPDATE ON videos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_posts_updated_at BEFORE UPDATE ON posts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_books_updated_at BEFORE UPDATE ON books FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_readings_updated_at BEFORE UPDATE ON readings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
