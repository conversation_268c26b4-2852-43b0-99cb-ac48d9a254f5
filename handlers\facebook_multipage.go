package handlers

import (
	"net/http"
	"strconv"
	"time"

	"recovery-dashboard/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// MultiPageFacebookHandler handles multi-page Facebook operations
type MultiPageFacebookHandler struct {
	db                       *gorm.DB
	multiPageFacebookService *services.MultiPageFacebookService
}

// NewMultiPageFacebookHandler creates a new multi-page Facebook handler
func NewMultiPageFacebookHandler(db *gorm.DB, multiPageService *services.MultiPageFacebookService) *MultiPageFacebookHandler {
	return &MultiPageFacebookHandler{
		db:                       db,
		multiPageFacebookService: multiPageService,
	}
}

// GetAllPages returns all configured Facebook pages
func (h *MultiPageFacebookHandler) GetAllPages(c *gin.Context) {
	pages := h.multiPageFacebookService.GetAllPages()
	
	// Convert to response format
	response := make(map[string]interface{})
	for pageID, page := range pages {
		response[pageID] = gin.H{
			"id":          page.ID,
			"name":        page.Name,
			"brand_type":  page.BrandType,
			"description": page.Description,
			"configured":  page.Token != "",
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"pages": response,
		"count": len(pages),
	})
}

// GetAllPagesStatus returns the status of all configured pages including token validation
func (h *MultiPageFacebookHandler) GetAllPagesStatus(c *gin.Context) {
	status := h.multiPageFacebookService.GetAllPagesStatus()

	c.JSON(http.StatusOK, gin.H{
		"pages":  status,
		"count":  len(status),
		"status": "success",
	})
}

// GetPageInfo retrieves information about a specific page
func (h *MultiPageFacebookHandler) GetPageInfo(c *gin.Context) {
	pageID := c.Param("page_id")
	if pageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Page ID is required"})
		return
	}

	pageInfo, err := h.multiPageFacebookService.GetPageInfo(pageID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get page info: " + err.Error()})
		return
	}

	// Get page configuration
	page, err := h.multiPageFacebookService.GetPage(pageID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Page not configured: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"page_info": pageInfo,
		"config": gin.H{
			"brand_type":  page.BrandType,
			"description": page.Description,
		},
	})
}

// CreatePost creates a post on a specific page
func (h *MultiPageFacebookHandler) CreatePost(c *gin.Context) {
	pageID := c.Param("page_id")
	if pageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Page ID is required"})
		return
	}

	var req struct {
		Message       string `json:"message" binding:"required"`
		Link          string `json:"link"`
		ScheduledTime string `json:"scheduled_time"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	postReq := services.CreatePostRequest{
		Message:       req.Message,
		Link:          req.Link,
		ScheduledTime: req.ScheduledTime,
	}

	postID, err := h.multiPageFacebookService.CreatePost(pageID, postReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create post: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"post_id": postID,
		"page_id": pageID,
		"message": "Post created successfully",
	})
}

// CrossPostYouTubeVideo posts a YouTube video to a specific page with brand-appropriate messaging
func (h *MultiPageFacebookHandler) CrossPostYouTubeVideo(c *gin.Context) {
	pageID := c.Param("page_id")
	if pageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Page ID is required"})
		return
	}

	var req struct {
		VideoID     string `json:"video_id" binding:"required"`
		VideoTitle  string `json:"video_title" binding:"required"`
		VideoURL    string `json:"video_url" binding:"required"`
		ScheduleFor string `json:"schedule_for"` // Optional: ISO 8601 timestamp
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	var scheduleTime *time.Time
	if req.ScheduleFor != "" {
		parsedTime, err := time.Parse(time.RFC3339, req.ScheduleFor)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid schedule time format. Use ISO 8601 format."})
			return
		}
		scheduleTime = &parsedTime
	}

	postID, err := h.multiPageFacebookService.CrossPostYouTubeVideo(pageID, req.VideoTitle, req.VideoURL, scheduleTime)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cross-post video: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":            true,
		"post_id":            postID,
		"page_id":            pageID,
		"video_id":           req.VideoID,
		"facebook_post_id":   postID,
		"message":            "YouTube video cross-posted to Facebook successfully",
		"scheduled":          scheduleTime != nil,
	})
}

// GetPagePosts retrieves recent posts from a specific page
func (h *MultiPageFacebookHandler) GetPagePosts(c *gin.Context) {
	pageID := c.Param("page_id")
	if pageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Page ID is required"})
		return
	}

	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 10
	}

	posts, err := h.multiPageFacebookService.GetPagePosts(pageID, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get page posts: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"posts":   posts,
		"count":   len(posts),
		"page_id": pageID,
	})
}

// GetPageInsights retrieves analytics for a specific page
func (h *MultiPageFacebookHandler) GetPageInsights(c *gin.Context) {
	pageID := c.Param("page_id")
	if pageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Page ID is required"})
		return
	}

	insights, err := h.multiPageFacebookService.GetPageInsights(pageID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get page insights: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"insights": insights,
		"page_id":  pageID,
	})
}

// CrossPostToAllPages posts content to all configured pages with brand-appropriate messaging
func (h *MultiPageFacebookHandler) CrossPostToAllPages(c *gin.Context) {
	var req struct {
		VideoID     string `json:"video_id" binding:"required"`
		VideoTitle  string `json:"video_title" binding:"required"`
		VideoURL    string `json:"video_url" binding:"required"`
		ScheduleFor string `json:"schedule_for"` // Optional: ISO 8601 timestamp
		ExcludePages []string `json:"exclude_pages"` // Optional: page IDs to exclude
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	var scheduleTime *time.Time
	if req.ScheduleFor != "" {
		parsedTime, err := time.Parse(time.RFC3339, req.ScheduleFor)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid schedule time format. Use ISO 8601 format."})
			return
		}
		scheduleTime = &parsedTime
	}

	// Create exclude map for faster lookup
	excludeMap := make(map[string]bool)
	for _, pageID := range req.ExcludePages {
		excludeMap[pageID] = true
	}

	results := make(map[string]interface{})
	pages := h.multiPageFacebookService.GetAllPages()

	for pageID, page := range pages {
		// Skip excluded pages
		if excludeMap[pageID] {
			results[pageID] = gin.H{
				"success": false,
				"message": "Excluded from posting",
				"page_name": page.Name,
			}
			continue
		}

		// Skip pages without tokens
		if page.Token == "" {
			results[pageID] = gin.H{
				"success": false,
				"message": "No token configured",
				"page_name": page.Name,
			}
			continue
		}

		postID, err := h.multiPageFacebookService.CrossPostYouTubeVideo(pageID, req.VideoTitle, req.VideoURL, scheduleTime)
		if err != nil {
			results[pageID] = gin.H{
				"success": false,
				"error": err.Error(),
				"page_name": page.Name,
			}
		} else {
			results[pageID] = gin.H{
				"success": true,
				"post_id": postID,
				"page_name": page.Name,
				"brand_type": page.BrandType,
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"video_id": req.VideoID,
		"results": results,
		"scheduled": scheduleTime != nil,
		"message": "Cross-posting completed",
	})
}
