# YouTube Integration - Sound of Recovery Channel

This document outlines the YouTube integration for the "Sound of Recovery" channel, designed to automate the upload and management of your 2000+ AA/NA speaker tapes.

## 🎯 Overview

The YouTube integration provides:
- **Automated Video Uploads**: Upload processed speaker videos with standardized metadata
- **Channel Analytics**: Track performance metrics for the Sound of Recovery channel
- **Playlist Management**: Organize content by speaker program, topic, or series
- **Content Pipeline Integration**: Seamless workflow from audio processing to YouTube publication

## 🔧 Setup Requirements

### 1. YouTube API Credentials

You'll need to set up YouTube Data API v3 credentials:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable YouTube Data API v3
4. Create credentials (API Key + OAuth 2.0)
5. Add your credentials to `.env`:

```env
YOUTUBE_API_KEY=your-youtube-data-api-key
YOUTUBE_CLIENT_ID=your-youtube-oauth-client-id
YOUTUBE_CLIENT_SECRET=your-youtube-oauth-client-secret
YOUTUBE_CHANNEL_ID=your-sound-of-recovery-channel-id
```

### 2. Channel ID Discovery

To find your Sound of Recovery channel ID:
```bash
# Using YouTube API
curl "https://www.googleapis.com/youtube/v3/channels?part=id&forUsername=SoundOfRecovery&key=YOUR_API_KEY"

# Or check channel URL: youtube.com/channel/CHANNEL_ID
```

## 📡 API Endpoints

### Channel Management

#### Get Channel Analytics
```http
GET /api/v1/youtube/channel/analytics
```

**Response:**
```json
{
  "analytics": {
    "channel_id": "UC...",
    "channel_title": "Sound of Recovery",
    "subscriber_count": 1250,
    "video_count": 89,
    "view_count": 45600,
    "description": "Sharing powerful stories of hope and healing..."
  }
}
```

#### Get Channel Videos
```http
GET /api/v1/youtube/videos?max_results=50
```

### Video Management

#### Upload Video
```http
POST /api/v1/youtube/upload
Content-Type: application/json

{
  "speaker_id": 1,
  "video_path": "/path/to/processed/video.mp4",
  "topic": "Step 4 - Fearless Moral Inventory",
  "privacy": "public",
  "category_id": "22"
}
```

**Response:**
```json
{
  "message": "Video uploaded successfully",
  "video_id": "dQw4w9WgXcQ",
  "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
  "title": "John D. - Step 4 | AA Recovery Speaker | Sound of Recovery"
}
```

#### Get Video Analytics
```http
GET /api/v1/youtube/videos/{video_id}/analytics
```

### Content Pipeline Integration

#### Process Speaker Video
```http
POST /api/v1/youtube/speakers/{speaker_id}/process
Content-Type: application/json

{
  "topic": "Daily Reflections",
  "privacy": "public",
  "auto_upload": true
}
```

#### Get Upload Status
```http
GET /api/v1/youtube/upload-status
```

### Playlist Management

#### Create Playlist
```http
POST /api/v1/youtube/playlists
Content-Type: application/json

{
  "title": "AA Step Work Series",
  "description": "Collection of AA speakers discussing the 12 steps"
}
```

#### Add Video to Playlist
```http
POST /api/v1/youtube/playlists/add-video
Content-Type: application/json

{
  "playlist_id": "PLrAXtmRdnEQy...",
  "video_id": "dQw4w9WgXcQ"
}
```

## 🎬 Content Standards

### Video Titles
The system automatically generates standardized titles:
```
{Speaker Name} - {Topic} | {Program} Recovery Speaker | Sound of Recovery
```

Examples:
- "John D. - Step 4 | AA Recovery Speaker | Sound of Recovery"
- "Sarah M. | NA Recovery Speaker | Sound of Recovery"

### Video Descriptions
Standardized descriptions include:
- Speaker information and program
- Topic (if specified)
- Channel branding and call-to-action
- Relevant hashtags (#Recovery, #AA, #NA, etc.)

### Tags
Automatically generated tags:
- recovery, sobriety, addiction recovery
- Program-specific: AA, NA, alcoholics anonymous, narcotics anonymous
- Topic-specific tags when provided
- Channel branding: sound of recovery

### Categories
- Default: "22" (People & Blogs)
- Can be customized per upload

## 🔄 Content Pipeline Workflow

### 1. Audio Processing
```
Raw Speaker Tape → Cleaned Audio → Transcription
```

### 2. Video Rendering
```
Audio + Transcript → After Effects → Rendered Video
```

### 3. YouTube Upload
```
Rendered Video → Metadata Generation → YouTube Upload → Database Update
```

### 4. Post-Processing
```
YouTube Response → Analytics Sync → Playlist Assignment
```

## 📊 Analytics Integration

### Tracked Metrics
- **Views**: Total and recent view counts
- **Engagement**: Likes, comments, shares
- **Audience**: Demographics and retention
- **Performance**: Click-through rates, watch time

### Sync Schedule
- Real-time: Upload status and immediate metrics
- Daily: View counts and engagement metrics
- Weekly: Detailed analytics and audience insights

## 🎯 Playlist Organization

### Suggested Playlist Structure
- **By Program**: "AA Speakers", "NA Speakers"
- **By Topic**: "Step Work", "Sponsorship", "Daily Reflections"
- **By Series**: "Beginner's Guide", "Long-term Recovery"
- **By Speaker**: Individual speaker collections

### Auto-Playlist Assignment
Videos can be automatically added to playlists based on:
- Speaker program (AA/NA)
- Topic keywords
- Speaker name
- Upload date

## 🔐 Privacy & Permissions

### Upload Privacy Options
- **Public**: Immediately visible to all users
- **Unlisted**: Accessible via direct link only
- **Private**: Only visible to channel owner

### Recommended Workflow
1. Upload as "Unlisted" for review
2. Manual approval process
3. Change to "Public" when ready

## 🚀 Getting Started

### 1. Test Channel Connection
```bash
curl http://localhost:8080/api/v1/youtube/channel/analytics
```

### 2. Upload Test Video
```bash
curl -X POST http://localhost:8080/api/v1/youtube/upload \
  -H "Content-Type: application/json" \
  -d '{
    "speaker_id": 1,
    "video_path": "/path/to/test-video.mp4",
    "topic": "Test Upload",
    "privacy": "private"
  }'
```

### 3. Monitor Upload Status
```bash
curl http://localhost:8080/api/v1/youtube/upload-status
```

## 📈 Success Metrics

### Channel Growth Targets
- **Subscribers**: 5,000 by end of year
- **Videos**: 500+ speaker tapes uploaded
- **Views**: 100,000+ total channel views
- **Engagement**: 5%+ average engagement rate

### Content Performance
- **Upload Frequency**: 3-5 videos per week
- **Average Watch Time**: 15+ minutes
- **Retention Rate**: 60%+ at 5 minutes
- **Click-through Rate**: 8%+ from impressions

## 🛠️ Troubleshooting

### Common Issues
1. **API Quota Exceeded**: YouTube API has daily quotas
2. **Upload Failures**: Check file format and size limits
3. **Authentication Errors**: Verify OAuth credentials
4. **Metadata Errors**: Ensure required fields are provided

### Error Handling
The system includes comprehensive error handling for:
- Network timeouts
- API rate limiting
- Invalid file formats
- Authentication failures

## 🔮 Future Enhancements

### Planned Features
- **Automated Thumbnails**: AI-generated custom thumbnails
- **Scheduled Uploads**: Queue videos for future publication
- **A/B Testing**: Test different titles and descriptions
- **Advanced Analytics**: Custom reporting and insights
- **Live Streaming**: Support for live recovery meetings
