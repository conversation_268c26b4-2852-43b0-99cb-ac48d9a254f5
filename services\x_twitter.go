package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// XService handles X (Twitter) API interactions
type XService struct {
	AccessToken string
	BaseURL     string
}

// NewXService creates a new X service instance
func NewXService(accessToken string) *XService {
	return &XService{
		AccessToken: accessToken,
		BaseURL:     "https://api.twitter.com/2",
	}
}

// XUser represents X user information
type XUser struct {
	ID              string `json:"id"`
	Username        string `json:"username"`
	Name            string `json:"name"`
	Description     string `json:"description"`
	FollowersCount  int    `json:"public_metrics.followers_count"`
	FollowingCount  int    `json:"public_metrics.following_count"`
	TweetCount      int    `json:"public_metrics.tweet_count"`
	ListedCount     int    `json:"public_metrics.listed_count"`
	ProfileImageURL string `json:"profile_image_url"`
	Verified        bool   `json:"verified"`
}

// XTweet represents an X tweet
type XTweet struct {
	ID              string    `json:"id"`
	Text            string    `json:"text"`
	CreatedAt       time.Time `json:"created_at"`
	AuthorID        string    `json:"author_id"`
	ConversationID  string    `json:"conversation_id"`
	RetweetCount    int       `json:"public_metrics.retweet_count"`
	LikeCount       int       `json:"public_metrics.like_count"`
	ReplyCount      int       `json:"public_metrics.reply_count"`
	QuoteCount      int       `json:"public_metrics.quote_count"`
	BookmarkCount   int       `json:"public_metrics.bookmark_count"`
	ImpressionCount int       `json:"public_metrics.impression_count"`
}

// XAnalytics represents X analytics data
type XAnalytics struct {
	Impressions   int `json:"impressions"`
	Engagements   int `json:"engagements"`
	Likes         int `json:"likes"`
	Retweets      int `json:"retweets"`
	Replies       int `json:"replies"`
	Quotes        int `json:"quotes"`
	Bookmarks     int `json:"bookmarks"`
	ProfileClicks int `json:"profile_clicks"`
	URLClicks     int `json:"url_clicks"`
}

// XCreateTweetRequest represents a request to create an X tweet
type XCreateTweetRequest struct {
	Text       string   `json:"text"`
	MediaIDs   []string `json:"media_ids,omitempty"`
	ReplyTo    string   `json:"reply.in_reply_to_tweet_id,omitempty"`
	QuoteTweet string   `json:"quote_tweet_id,omitempty"`
}

// GetUserInfo retrieves X user information
func (xs *XService) GetUserInfo() (*XUser, error) {
	url := fmt.Sprintf("%s/users/me?user.fields=description,public_metrics,profile_image_url,verified", xs.BaseURL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+xs.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("X API error: %s", string(body))
	}

	var response struct {
		Data XUser `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode user info: %w", err)
	}

	return &response.Data, nil
}

// CreateTweet creates an X tweet
func (xs *XService) CreateTweet(req XCreateTweetRequest) (string, error) {
	url := fmt.Sprintf("%s/tweets", xs.BaseURL)

	jsonBody, err := json.Marshal(req)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Authorization", "Bearer "+xs.AccessToken)
	httpReq.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return "", fmt.Errorf("failed to create tweet: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("X API error: %s", string(body))
	}

	var response struct {
		Data struct {
			ID   string `json:"id"`
			Text string `json:"text"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	return response.Data.ID, nil
}

// GetUserTweets retrieves user's X tweets
func (xs *XService) GetUserTweets(limit int) ([]XTweet, error) {
	url := fmt.Sprintf("%s/users/me/tweets?tweet.fields=created_at,author_id,conversation_id,public_metrics&max_results=%d", xs.BaseURL, limit)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+xs.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get tweets: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("X API error: %s", string(body))
	}

	var response struct {
		Data []XTweet `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode tweets: %w", err)
	}

	return response.Data, nil
}

// GetAnalytics retrieves X analytics
func (xs *XService) GetAnalytics(days int) (*XAnalytics, error) {
	// Mock analytics for now since X API analytics require special access
	analytics := &XAnalytics{
		Impressions:   15600,
		Engagements:   890,
		Likes:         456,
		Retweets:      123,
		Replies:       67,
		Quotes:        34,
		Bookmarks:     89,
		ProfileClicks: 45,
		URLClicks:     156,
	}

	return analytics, nil
}

// ValidateToken checks if the X access token is valid
func (xs *XService) ValidateToken() error {
	_, err := xs.GetUserInfo()
	return err
}

// GenerateRecoveryTweet creates X-appropriate content from YouTube videos
func (xs *XService) GenerateRecoveryTweet(videoTitle, videoURL, accountType string) XCreateTweetRequest {
	var text string

	switch accountType {
	case "merch":
		text = fmt.Sprintf("🔥 New recovery content + merch drop!\n\n%s\n\nRecovery gear that matters 🙏\n\n#Recovery #RecoveryMerch #Sobriety #OneDayAtATime\n\n%s", videoTitle, videoURL)
	case "memes":
		text = fmt.Sprintf("Recovery mood 😅\n\n%s\n\nWhen the program hits different 💯\n\n#RecoveryMemes #SoberLife #RecoveryHumor #AA #NA\n\n%s", videoTitle, videoURL)
	case "serious":
		text = fmt.Sprintf("Powerful recovery wisdom 🙏\n\n%s\n\nEvery story matters. Keep going.\n\n#Recovery #Hope #Healing #SoundOfRecovery\n\n%s", videoTitle, videoURL)
	default:
		text = fmt.Sprintf("New recovery content:\n\n%s\n\n#Recovery #Sobriety #Hope\n\n%s", videoTitle, videoURL)
	}

	// Ensure tweet is under 280 characters
	if len(text) > 280 {
		// Truncate and add ellipsis
		text = text[:277] + "..."
	}

	return XCreateTweetRequest{
		Text: text,
	}
}

// CrossPostYouTubeVideo creates an X tweet for a YouTube video
func (xs *XService) CrossPostYouTubeVideo(videoTitle, videoURL, accountType string) (string, error) {
	tweetContent := xs.GenerateRecoveryTweet(videoTitle, videoURL, accountType)
	return xs.CreateTweet(tweetContent)
}
