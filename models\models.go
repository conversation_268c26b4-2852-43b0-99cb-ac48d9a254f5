package models

import (
	"time"

	"gorm.io/gorm"
)

// User represents system users with different roles
type User struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Email       string    `json:"email" gorm:"uniqueIndex;not null"`
	Name        string    `json:"name" gorm:"not null"`
	Role        string    `json:"role" gorm:"not null;default:'editor'"` // admin, va, editor
	Permissions string    `json:"permissions" gorm:"type:text"`           // JSON string of permissions
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Brands []Brand `json:"brands" gorm:"many2many:user_brands;"`
}

// Brand represents different content brands
type Brand struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"not null"`
	Type        string    `json:"type" gorm:"not null"` // humor, serious, spiritual
	Description string    `json:"description" gorm:"type:text"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Posts []Post `json:"posts"`
	Users []User `json:"users" gorm:"many2many:user_brands;"`
}

// Speaker represents recovery speakers
type Speaker struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Name      string    `json:"name" gorm:"not null"`
	Program   string    `json:"program" gorm:"not null"` // AA, NA, etc.
	AudioPath string    `json:"audio_path"`
	Tags      string    `json:"tags" gorm:"type:text"` // JSON array of tags
	Status    string    `json:"status" gorm:"default:'pending'"` // pending, processed, published
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Videos []Video `json:"videos"`
}

// Video represents rendered video content
type Video struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	SpeakerID   uint      `json:"speaker_id" gorm:"not null"`
	Title       string    `json:"title" gorm:"not null"`
	RenderState string    `json:"render_state" gorm:"default:'pending'"` // pending, rendering, completed, failed
	URL         string    `json:"url"`
	Duration    int       `json:"duration"` // in seconds
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Speaker Speaker `json:"speaker" gorm:"foreignKey:SpeakerID"`
}

// Post represents social media posts
type Post struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	BrandID     uint      `json:"brand_id" gorm:"not null"`
	Platform    string    `json:"platform" gorm:"not null"` // youtube, facebook, instagram, tiktok
	Caption     string    `json:"caption" gorm:"type:text"`
	AssetPath   string    `json:"asset_path"`
	Status      string    `json:"status" gorm:"default:'draft'"` // draft, scheduled, posted, failed
	ScheduledAt *time.Time `json:"scheduled_at"`
	PostedAt    *time.Time `json:"posted_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Brand Brand `json:"brand" gorm:"foreignKey:BrandID"`
}

// Product represents Shopify products
type Product struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	ShopifyID   string    `json:"shopify_id" gorm:"uniqueIndex;not null"`
	Title       string    `json:"title" gorm:"not null"`
	Price       float64   `json:"price"`
	Inventory   int       `json:"inventory"`
	Status      string    `json:"status" gorm:"default:'active'"` // active, inactive, archived
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Orders []Order `json:"orders" gorm:"many2many:order_products;"`
}

// Order represents Shopify orders
type Order struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	ShopifyID  string    `json:"shopify_id" gorm:"uniqueIndex;not null"`
	BuyerEmail string    `json:"buyer_email"`
	BuyerName  string    `json:"buyer_name"`
	Total      float64   `json:"total"`
	Status     string    `json:"status" gorm:"not null"` // pending, paid, shipped, delivered, cancelled
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Products []Product `json:"products" gorm:"many2many:order_products;"`
}

// Reading represents daily readings
type Reading struct {
	ID       uint      `json:"id" gorm:"primaryKey"`
	Date     time.Time `json:"date" gorm:"uniqueIndex;not null"`
	Text     string    `json:"text" gorm:"type:text;not null"`
	AudioURL string    `json:"audio_url"`
	BookID   uint      `json:"book_id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Book Book `json:"book" gorm:"foreignKey:BookID"`
}

// Book represents recovery books
type Book struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Title       string    `json:"title" gorm:"not null"`
	Author      string    `json:"author"`
	Description string    `json:"description" gorm:"type:text"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Readings []Reading `json:"readings"`
}

// AutoMigrate runs database migrations
func AutoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&User{},
		&Brand{},
		&Speaker{},
		&Video{},
		&Post{},
		&Product{},
		&Order{},
		&Reading{},
		&Book{},
	)
}
