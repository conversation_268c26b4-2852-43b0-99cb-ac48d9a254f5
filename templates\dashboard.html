<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-blue-600 text-white p-4">
            <div class="container mx-auto flex justify-between items-center">
                <h1 class="text-xl font-bold">Recovery Dashboard</h1>
                <div class="space-x-4">
                    <a href="/api/v1/metrics" class="hover:text-blue-200">Metrics</a>
                    <a href="/api/v1/content/speakers" class="hover:text-blue-200">Speakers</a>
                    <a href="/api/v1/social/posts" class="hover:text-blue-200">Posts</a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="container mx-auto p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Metrics Cards -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-700">Total Speakers</h3>
                    <p class="text-3xl font-bold text-blue-600" id="total-speakers">-</p>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-700">Videos Rendered</h3>
                    <p class="text-3xl font-bold text-green-600" id="total-videos">-</p>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-700">Posts Published</h3>
                    <p class="text-3xl font-bold text-purple-600" id="total-posts">-</p>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-700">Total Revenue</h3>
                    <p class="text-3xl font-bold text-yellow-600" id="total-revenue">-</p>
                </div>
            </div>

            <!-- YouTube Analytics Section -->
            <div class="bg-white rounded-lg shadow p-6 mb-8">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-800">📺 Sound of Recovery - YouTube Analytics</h2>
                    <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">LIVE DATA</span>
                </div>

                <!-- YouTube Metrics Grid -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-red-50 rounded-lg p-4 border border-red-200">
                        <div class="flex items-center">
                            <div class="bg-red-500 rounded-full p-2 mr-3">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                    <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Subscribers</p>
                                <p class="text-2xl font-bold text-red-600" id="youtube-subscribers">-</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                        <div class="flex items-center">
                            <div class="bg-blue-500 rounded-full p-2 mr-3">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Total Videos</p>
                                <p class="text-2xl font-bold text-blue-600" id="youtube-videos">-</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 rounded-lg p-4 border border-green-200">
                        <div class="flex items-center">
                            <div class="bg-green-500 rounded-full p-2 mr-3">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Total Views</p>
                                <p class="text-2xl font-bold text-green-600" id="youtube-views">-</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
                        <div class="flex items-center">
                            <div class="bg-purple-500 rounded-full p-2 mr-3">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Avg Views/Video</p>
                                <p class="text-2xl font-bold text-purple-600" id="youtube-avg-views">-</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- YouTube Management Actions -->
                <div class="border-t pt-4">
                    <div class="flex flex-wrap gap-3">
                        <button onclick="refreshYouTubeData()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                            </svg>
                            Refresh Data
                        </button>
                        <button onclick="viewChannelVideos()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                            </svg>
                            View All Videos
                        </button>
                        <button onclick="syncToDatabase()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                            </svg>
                            Sync to Database
                        </button>
                        <a href="https://youtube.com/channel/UC8ZFhjdOAFVbVfnxgXKvWTA" target="_blank" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.559-.5-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.559.5.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.498-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clip-rule="evenodd"/>
                            </svg>
                            Open Channel
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6 mb-8">
                <h2 class="text-xl font-bold mb-4">Quick Actions</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                        Add New Speaker
                    </button>
                    <button class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                        Schedule Post
                    </button>
                    <button class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded">
                        Sync Shopify
                    </button>
                </div>
            </div>

            <!-- Facebook Pages Section -->
            <div class="bg-white rounded-lg shadow p-6 mb-8">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-800">📘 Facebook Pages Management</h2>
                    <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">MULTI-PAGE</span>
                </div>

                <!-- Facebook Pages Grid -->
                <div id="facebook-pages" class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="text-center text-gray-500 py-8">Loading Facebook pages...</div>
                </div>

                <!-- Facebook Management Actions -->
                <div class="border-t pt-4">
                    <div class="flex flex-wrap gap-3">
                        <button onclick="refreshFacebookData()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                            </svg>
                            Refresh Status
                        </button>
                        <button onclick="testCrossPost()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                            </svg>
                            Test Cross-Post
                        </button>
                        <button onclick="viewAllPages()" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                            </svg>
                            Manage Pages
                        </button>
                    </div>
                </div>
            </div>

            <!-- Recent YouTube Videos -->
            <div class="bg-white rounded-lg shadow p-6 mb-8">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold text-gray-800">🎬 Recent YouTube Videos</h2>
                    <button onclick="loadRecentVideos()" class="text-blue-600 hover:text-blue-800 text-sm">Refresh</button>
                </div>
                <div id="recent-videos" class="space-y-4">
                    <div class="text-center text-gray-500 py-8">Loading videos...</div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-bold mb-4">Recent Activity</h2>
                <div class="space-y-3">
                    <div class="flex items-center justify-between border-b pb-2">
                        <span class="text-gray-700">New speaker added: John D.</span>
                        <span class="text-sm text-gray-500">2 hours ago</span>
                    </div>
                    <div class="flex items-center justify-between border-b pb-2">
                        <span class="text-gray-700">Video rendered: Daily Reflection #45</span>
                        <span class="text-sm text-gray-500">4 hours ago</span>
                    </div>
                    <div class="flex items-center justify-between border-b pb-2">
                        <span class="text-gray-700">Post scheduled for Instagram</span>
                        <span class="text-sm text-gray-500">6 hours ago</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Load dashboard metrics
        async function loadMetrics() {
            try {
                const response = await fetch('/api/v1/metrics/');
                const data = await response.json();

                if (data.metrics) {
                    document.getElementById('total-speakers').textContent = data.metrics.content.total_speakers || 0;
                    document.getElementById('total-videos').textContent = data.metrics.content.total_videos || 0;
                    document.getElementById('total-posts').textContent = data.metrics.social.total_posts || 0;
                    document.getElementById('total-revenue').textContent = '$' + (data.metrics.shopify.total_revenue || 0).toFixed(2);
                }
            } catch (error) {
                console.error('Failed to load metrics:', error);
            }
        }

        // Load YouTube channel analytics
        async function loadYouTubeAnalytics() {
            try {
                const response = await fetch('/api/v1/youtube/channel/analytics');
                const data = await response.json();

                if (data.analytics) {
                    document.getElementById('youtube-subscribers').textContent = data.analytics.subscriber_count || 0;
                    document.getElementById('youtube-videos').textContent = data.analytics.video_count || 0;
                    document.getElementById('youtube-views').textContent = (data.analytics.view_count || 0).toLocaleString();

                    // Calculate average views per video
                    const avgViews = data.analytics.video_count > 0 ?
                        Math.round(data.analytics.view_count / data.analytics.video_count) : 0;
                    document.getElementById('youtube-avg-views').textContent = avgViews.toLocaleString();
                }
            } catch (error) {
                console.error('Failed to load YouTube analytics:', error);
                // Show error state
                document.getElementById('youtube-subscribers').textContent = 'Error';
                document.getElementById('youtube-videos').textContent = 'Error';
                document.getElementById('youtube-views').textContent = 'Error';
                document.getElementById('youtube-avg-views').textContent = 'Error';
            }
        }

        // Load recent YouTube videos
        async function loadRecentVideos() {
            try {
                const response = await fetch('/api/v1/youtube/videos?max_results=5');
                const data = await response.json();

                const container = document.getElementById('recent-videos');

                if (data.videos && data.videos.length > 0) {
                    container.innerHTML = data.videos.map(video => `
                        <div class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <img src="${video.snippet.thumbnails.medium.url}"
                                 alt="${video.snippet.title}"
                                 class="w-24 h-18 object-cover rounded">
                            <div class="flex-1">
                                <h3 class="font-semibold text-gray-800 line-clamp-2">${video.snippet.title}</h3>
                                <p class="text-sm text-gray-600 mt-1">Published: ${new Date(video.snippet.publishedAt).toLocaleDateString()}</p>
                                <div class="flex items-center space-x-4 mt-2">
                                    <button onclick="getVideoAnalytics('${video.id.videoId}')"
                                            class="text-blue-600 hover:text-blue-800 text-sm">View Analytics</button>
                                    <a href="https://youtube.com/watch?v=${video.id.videoId}"
                                       target="_blank"
                                       class="text-red-600 hover:text-red-800 text-sm">Watch on YouTube</a>
                                </div>
                            </div>
                        </div>
                    `).join('');
                } else {
                    container.innerHTML = '<div class="text-center text-gray-500 py-8">No videos found</div>';
                }
            } catch (error) {
                console.error('Failed to load recent videos:', error);
                document.getElementById('recent-videos').innerHTML =
                    '<div class="text-center text-red-500 py-8">Failed to load videos</div>';
            }
        }

        // YouTube management functions
        async function refreshYouTubeData() {
            await Promise.all([loadYouTubeAnalytics(), loadRecentVideos()]);
            alert('YouTube data refreshed!');
        }

        function viewChannelVideos() {
            window.open('/api/v1/youtube/videos', '_blank');
        }

        async function syncToDatabase() {
            // This would sync YouTube videos to your local database
            alert('Sync to database feature - coming soon!');
        }

        async function getVideoAnalytics(videoId) {
            try {
                const response = await fetch(`/api/v1/youtube/videos/${videoId}/analytics`);
                const data = await response.json();

                if (data.analytics) {
                    const analytics = data.analytics;
                    alert(`Video Analytics:\\n\\nTitle: ${analytics.title}\\nViews: ${analytics.views}\\nLikes: ${analytics.likes}\\nComments: ${analytics.comments}\\nDuration: ${analytics.duration}`);
                }
            } catch (error) {
                console.error('Failed to load video analytics:', error);
                alert('Failed to load video analytics');
            }
        }

        // Load Facebook pages status
        async function loadFacebookPages() {
            try {
                const response = await fetch('/api/v1/facebook-multi/pages/status');
                const data = await response.json();

                const container = document.getElementById('facebook-pages');

                if (data.pages && Object.keys(data.pages).length > 0) {
                    container.innerHTML = Object.entries(data.pages).map(([pageId, page]) => `
                        <div class="border border-gray-200 rounded-lg p-4 ${page.token_valid ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="font-semibold text-gray-800">${page.name}</h3>
                                <span class="px-2 py-1 rounded-full text-xs font-medium ${page.token_valid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                    ${page.token_valid ? 'CONNECTED' : 'NEEDS SETUP'}
                                </span>
                            </div>
                            <div class="space-y-2">
                                <div class="flex items-center text-sm">
                                    <span class="w-20 text-gray-600">Type:</span>
                                    <span class="capitalize font-medium ${page.brand_type === 'humor' ? 'text-yellow-600' : page.brand_type === 'spiritual' ? 'text-purple-600' : 'text-blue-600'}">${page.brand_type}</span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <span class="w-20 text-gray-600">Status:</span>
                                    <span class="${page.configured ? 'text-green-600' : 'text-red-600'}">${page.configured ? 'Configured' : 'Not Configured'}</span>
                                </div>
                                ${!page.token_valid && page.error ? `
                                    <div class="mt-2 p-2 bg-red-100 rounded text-xs text-red-700">
                                        <strong>Error:</strong> ${page.error.includes('pages_read_engagement') ? 'Missing permissions. Need pages_read_engagement permission.' : 'Token validation failed.'}
                                    </div>
                                ` : ''}
                            </div>
                            <div class="mt-3 flex space-x-2">
                                <button onclick="viewPageDetails('${pageId}')" class="text-blue-600 hover:text-blue-800 text-xs">View Details</button>
                                ${page.token_valid ? `<button onclick="testPagePost('${pageId}')" class="text-green-600 hover:text-green-800 text-xs">Test Post</button>` : ''}
                            </div>
                        </div>
                    `).join('');
                } else {
                    container.innerHTML = '<div class="text-center text-gray-500 py-8">No Facebook pages configured</div>';
                }
            } catch (error) {
                console.error('Failed to load Facebook pages:', error);
                document.getElementById('facebook-pages').innerHTML =
                    '<div class="text-center text-red-500 py-8">Failed to load Facebook pages</div>';
            }
        }

        // Facebook management functions
        async function refreshFacebookData() {
            await loadFacebookPages();
            alert('Facebook pages data refreshed!');
        }

        async function testCrossPost() {
            try {
                // Get the latest YouTube video for testing
                const youtubeResponse = await fetch('/api/v1/youtube/videos?max_results=1');
                const youtubeData = await youtubeResponse.json();

                if (youtubeData.videos && youtubeData.videos.length > 0) {
                    const video = youtubeData.videos[0];
                    const testData = {
                        video_id: video.id.videoId,
                        video_title: video.snippet.title,
                        video_url: `https://youtube.com/watch?v=${video.id.videoId}`
                    };

                    const response = await fetch('/api/v1/facebook-multi/cross-post-all', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(testData)
                    });

                    const result = await response.json();

                    if (result.success) {
                        alert(`Cross-post test completed!\\n\\nResults:\\n${Object.entries(result.results).map(([pageId, res]) =>
                            `${res.page_name}: ${res.success ? 'Success' : 'Failed - ' + (res.error || res.message)}`
                        ).join('\\n')}`);
                    } else {
                        alert('Cross-post test failed: ' + (result.error || 'Unknown error'));
                    }
                } else {
                    alert('No YouTube videos found to test with');
                }
            } catch (error) {
                console.error('Failed to test cross-post:', error);
                alert('Failed to test cross-post: ' + error.message);
            }
        }

        function viewAllPages() {
            window.open('/api/v1/facebook-multi/pages', '_blank');
        }

        async function viewPageDetails(pageId) {
            try {
                const response = await fetch(`/api/v1/facebook-multi/pages/${pageId}`);
                const data = await response.json();

                if (data.page_info) {
                    const info = data.page_info;
                    alert(`Page Details:\\n\\nName: ${info.name}\\nCategory: ${info.category}\\nFollowers: ${info.followers_count || 'N/A'}\\nFans: ${info.fan_count || 'N/A'}\\nAbout: ${info.about || 'N/A'}`);
                } else {
                    alert('Failed to load page details');
                }
            } catch (error) {
                console.error('Failed to load page details:', error);
                alert('Failed to load page details: ' + error.message);
            }
        }

        async function testPagePost(pageId) {
            const message = prompt('Enter a test message to post:');
            if (message) {
                try {
                    const response = await fetch(`/api/v1/facebook-multi/pages/${pageId}/posts`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: message
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        alert(`Post created successfully!\\nPost ID: ${result.post_id}`);
                    } else {
                        alert('Failed to create post: ' + (result.error || 'Unknown error'));
                    }
                } catch (error) {
                    console.error('Failed to create post:', error);
                    alert('Failed to create post: ' + error.message);
                }
            }
        }

        // Load all data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadMetrics();
            loadYouTubeAnalytics();
            loadRecentVideos();
            loadFacebookPages();
        });
    </script>
</body>
</html>
