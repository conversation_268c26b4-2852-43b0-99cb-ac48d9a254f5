<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-blue-600 text-white p-4">
            <div class="container mx-auto flex justify-between items-center">
                <h1 class="text-xl font-bold">Recovery Dashboard</h1>
                <div class="space-x-4">
                    <a href="/api/v1/metrics" class="hover:text-blue-200">Metrics</a>
                    <a href="/api/v1/content/speakers" class="hover:text-blue-200">Speakers</a>
                    <a href="/api/v1/social/posts" class="hover:text-blue-200">Posts</a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="container mx-auto p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Metrics Cards -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-700">Total Speakers</h3>
                    <p class="text-3xl font-bold text-blue-600" id="total-speakers">-</p>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-700">Videos Rendered</h3>
                    <p class="text-3xl font-bold text-green-600" id="total-videos">-</p>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-700">Posts Published</h3>
                    <p class="text-3xl font-bold text-purple-600" id="total-posts">-</p>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-700">Total Revenue</h3>
                    <p class="text-3xl font-bold text-yellow-600" id="total-revenue">-</p>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6 mb-8">
                <h2 class="text-xl font-bold mb-4">Quick Actions</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                        Add New Speaker
                    </button>
                    <button class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                        Schedule Post
                    </button>
                    <button class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded">
                        Sync Shopify
                    </button>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-bold mb-4">Recent Activity</h2>
                <div class="space-y-3">
                    <div class="flex items-center justify-between border-b pb-2">
                        <span class="text-gray-700">New speaker added: John D.</span>
                        <span class="text-sm text-gray-500">2 hours ago</span>
                    </div>
                    <div class="flex items-center justify-between border-b pb-2">
                        <span class="text-gray-700">Video rendered: Daily Reflection #45</span>
                        <span class="text-sm text-gray-500">4 hours ago</span>
                    </div>
                    <div class="flex items-center justify-between border-b pb-2">
                        <span class="text-gray-700">Post scheduled for Instagram</span>
                        <span class="text-sm text-gray-500">6 hours ago</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Load dashboard metrics
        async function loadMetrics() {
            try {
                const response = await fetch('/api/v1/metrics/');
                const data = await response.json();
                
                if (data.metrics) {
                    document.getElementById('total-speakers').textContent = data.metrics.content.total_speakers || 0;
                    document.getElementById('total-videos').textContent = data.metrics.content.total_videos || 0;
                    document.getElementById('total-posts').textContent = data.metrics.social.total_posts || 0;
                    document.getElementById('total-revenue').textContent = '$' + (data.metrics.shopify.total_revenue || 0).toFixed(2);
                }
            } catch (error) {
                console.error('Failed to load metrics:', error);
            }
        }

        // Load metrics on page load
        document.addEventListener('DOMContentLoaded', loadMetrics);
    </script>
</body>
</html>
