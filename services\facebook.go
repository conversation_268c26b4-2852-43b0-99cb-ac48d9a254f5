package services

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
)

// FacebookService handles Facebook API interactions
type FacebookService struct {
	AccessToken string
	BaseURL     string
}

// NewFacebookService creates a new Facebook service instance
func NewFacebookService(accessToken string) *FacebookService {
	return &FacebookService{
		AccessToken: accessToken,
		BaseURL:     "https://graph.facebook.com/v18.0",
	}
}

// PageInfo represents Facebook page information
type PageInfo struct {
	ID             string `json:"id"`
	Name           string `json:"name"`
	Category       string `json:"category"`
	FollowersCount int    `json:"followers_count"`
	FanCount       int    `json:"fan_count"`
	About          string `json:"about"`
	Website        string `json:"website"`
	Picture        struct {
		Data struct {
			URL string `json:"url"`
		} `json:"data"`
	} `json:"picture"`
}

// PostData represents a Facebook post
type PostData struct {
	ID          string    `json:"id"`
	Message     string    `json:"message"`
	CreatedTime time.Time `json:"created_time"`
	Likes       struct {
		Summary struct {
			TotalCount int `json:"total_count"`
		} `json:"summary"`
	} `json:"likes"`
	Comments struct {
		Summary struct {
			TotalCount int `json:"total_count"`
		} `json:"summary"`
	} `json:"comments"`
	Shares struct {
		Count int `json:"count"`
	} `json:"shares"`
}

// PageInsights represents Facebook page analytics
type PageInsights struct {
	PageViews       int `json:"page_views"`
	PageImpressions int `json:"page_impressions"`
	PageEngagement  int `json:"page_engagement"`
	ReachTotal      int `json:"reach_total"`
}

// CreatePostRequest represents a request to create a Facebook post
type CreatePostRequest struct {
	Message         string `json:"message"`
	Link            string `json:"link,omitempty"`
	ScheduledTime   string `json:"scheduled_publish_time,omitempty"`
	PublishedStatus bool   `json:"published"`
}

// GetPageInfo retrieves information about a Facebook page
func (fs *FacebookService) GetPageInfo(pageID string) (*PageInfo, error) {
	url := fmt.Sprintf("%s/%s?fields=id,name,category,followers_count,fan_count,about,website,picture&access_token=%s",
		fs.BaseURL, pageID, fs.AccessToken)

	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to get page info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Facebook API error: %s", string(body))
	}

	var pageInfo PageInfo
	if err := json.NewDecoder(resp.Body).Decode(&pageInfo); err != nil {
		return nil, fmt.Errorf("failed to decode page info: %w", err)
	}

	return &pageInfo, nil
}

// GetPagePosts retrieves recent posts from a Facebook page
func (fs *FacebookService) GetPagePosts(pageID string, limit int) ([]PostData, error) {
	url := fmt.Sprintf("%s/%s/posts?fields=id,message,created_time,likes.summary(true),comments.summary(true),shares&limit=%d&access_token=%s",
		fs.BaseURL, pageID, limit, fs.AccessToken)

	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to get page posts: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Facebook API error: %s", string(body))
	}

	var response struct {
		Data []PostData `json:"data"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode posts: %w", err)
	}

	return response.Data, nil
}

// CreatePost creates a new post on a Facebook page
func (fs *FacebookService) CreatePost(pageID string, req CreatePostRequest) (string, error) {
	postURL := fmt.Sprintf("%s/%s/feed", fs.BaseURL, pageID)

	// Prepare form data
	data := url.Values{}
	data.Set("message", req.Message)
	data.Set("access_token", fs.AccessToken)

	if req.Link != "" {
		data.Set("link", req.Link)
	}

	if req.ScheduledTime != "" {
		data.Set("scheduled_publish_time", req.ScheduledTime)
		data.Set("published", "false")
	} else {
		data.Set("published", "true")
	}

	resp, err := http.PostForm(postURL, data)
	if err != nil {
		return "", fmt.Errorf("failed to create post: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("Facebook API error: %s", string(body))
	}

	var response struct {
		ID string `json:"id"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	return response.ID, nil
}

// GetPageInsights retrieves analytics for a Facebook page
func (fs *FacebookService) GetPageInsights(pageID string) (*PageInsights, error) {
	// Get insights for the last 30 days
	since := time.Now().AddDate(0, 0, -30).Unix()
	until := time.Now().Unix()

	url := fmt.Sprintf("%s/%s/insights?metric=page_views_total,page_impressions,page_engaged_users,page_fan_adds&since=%d&until=%d&access_token=%s",
		fs.BaseURL, pageID, since, until, fs.AccessToken)

	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to get page insights: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Facebook API error: %s", string(body))
	}

	var response struct {
		Data []struct {
			Name   string `json:"name"`
			Values []struct {
				Value int `json:"value"`
			} `json:"values"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode insights: %w", err)
	}

	insights := &PageInsights{}
	for _, metric := range response.Data {
		if len(metric.Values) > 0 {
			switch metric.Name {
			case "page_views_total":
				insights.PageViews = metric.Values[0].Value
			case "page_impressions":
				insights.PageImpressions = metric.Values[0].Value
			case "page_engaged_users":
				insights.PageEngagement = metric.Values[0].Value
			case "page_fan_adds":
				insights.ReachTotal = metric.Values[0].Value
			}
		}
	}

	return insights, nil
}

// ValidateToken checks if the Facebook access token is valid
func (fs *FacebookService) ValidateToken() error {
	url := fmt.Sprintf("%s/me?access_token=%s", fs.BaseURL, fs.AccessToken)

	resp, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("failed to validate token: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("invalid Facebook token: %s", string(body))
	}

	return nil
}

// GetManagedPages retrieves all pages managed by the current user
func (fs *FacebookService) GetManagedPages() ([]PageInfo, error) {
	url := fmt.Sprintf("%s/me/accounts?access_token=%s", fs.BaseURL, fs.AccessToken)

	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to get managed pages: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Facebook API error: %s", string(body))
	}

	var response struct {
		Data []PageInfo `json:"data"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode pages: %w", err)
	}

	return response.Data, nil
}
