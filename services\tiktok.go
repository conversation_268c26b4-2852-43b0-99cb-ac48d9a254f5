package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// TikTokService handles TikTok API interactions
type TikTokService struct {
	AccessToken string
	BaseURL     string
}

// NewTikTokService creates a new TikTok service instance
func NewTikTokService(accessToken string) *TikTokService {
	return &TikTokService{
		AccessToken: accessToken,
		BaseURL:     "https://open-api.tiktok.com",
	}
}

// TikTokUser represents TikTok user information
type TikTokUser struct {
	OpenID      string `json:"open_id"`
	UnionID     string `json:"union_id"`
	AvatarURL   string `json:"avatar_url"`
	DisplayName string `json:"display_name"`
	Username    string `json:"username"`
	Followers   int    `json:"follower_count"`
	Following   int    `json:"following_count"`
	Likes       int    `json:"likes_count"`
	VideoCount  int    `json:"video_count"`
}

// TikTokVideo represents a TikTok video
type TikTokVideo struct {
	ID          string    `json:"id"`
	Title       string    `json:"title"`
	VideoURL    string    `json:"video_url"`
	CoverURL    string    `json:"cover_image_url"`
	Duration    int       `json:"duration"`
	ViewCount   int       `json:"view_count"`
	LikeCount   int       `json:"like_count"`
	CommentCount int      `json:"comment_count"`
	ShareCount  int       `json:"share_count"`
	CreatedAt   time.Time `json:"create_time"`
}

// TikTokAnalytics represents TikTok analytics data
type TikTokAnalytics struct {
	ProfileViews int `json:"profile_view_count"`
	VideoViews   int `json:"video_view_count"`
	Likes        int `json:"likes_count"`
	Comments     int `json:"comments_count"`
	Shares       int `json:"shares_count"`
	Followers    int `json:"follower_count"`
}

// CreateVideoRequest represents a request to upload a video to TikTok
type CreateVideoRequest struct {
	VideoURL    string   `json:"video_url"`
	Title       string   `json:"title"`
	Description string   `json:"description"`
	Hashtags    []string `json:"hashtags"`
	Privacy     string   `json:"privacy_level"` // "PUBLIC", "FRIENDS", "SELF"
}

// GetUserInfo retrieves TikTok user information
func (ts *TikTokService) GetUserInfo() (*TikTokUser, error) {
	url := fmt.Sprintf("%s/v2/user/info/?fields=open_id,union_id,avatar_url,display_name,username,follower_count,following_count,likes_count,video_count", ts.BaseURL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+ts.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("TikTok API error: %s", string(body))
	}

	var response struct {
		Data  TikTokUser `json:"data"`
		Error struct {
			Code    string `json:"code"`
			Message string `json:"message"`
		} `json:"error"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode user info: %w", err)
	}

	if response.Error.Code != "" {
		return nil, fmt.Errorf("TikTok API error: %s - %s", response.Error.Code, response.Error.Message)
	}

	return &response.Data, nil
}

// GetUserVideos retrieves user's TikTok videos
func (ts *TikTokService) GetUserVideos(limit int) ([]TikTokVideo, error) {
	url := fmt.Sprintf("%s/v2/video/list/?fields=id,title,video_url,cover_image_url,duration,view_count,like_count,comment_count,share_count,create_time&max_count=%d", ts.BaseURL, limit)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+ts.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get videos: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("TikTok API error: %s", string(body))
	}

	var response struct {
		Data struct {
			Videos []TikTokVideo `json:"videos"`
		} `json:"data"`
		Error struct {
			Code    string `json:"code"`
			Message string `json:"message"`
		} `json:"error"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode videos: %w", err)
	}

	if response.Error.Code != "" {
		return nil, fmt.Errorf("TikTok API error: %s - %s", response.Error.Code, response.Error.Message)
	}

	return response.Data.Videos, nil
}

// UploadVideo uploads a video to TikTok
func (ts *TikTokService) UploadVideo(req CreateVideoRequest) (string, error) {
	url := fmt.Sprintf("%s/v2/post/", ts.BaseURL)

	// Prepare request body
	requestBody := map[string]interface{}{
		"post_info": map[string]interface{}{
			"title":         req.Title,
			"description":   req.Description,
			"privacy_level": req.Privacy,
		},
		"source_info": map[string]interface{}{
			"source":    "FILE_UPLOAD",
			"video_url": req.VideoURL,
		},
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Authorization", "Bearer "+ts.AccessToken)
	httpReq.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return "", fmt.Errorf("failed to upload video: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("TikTok API error: %s", string(body))
	}

	var response struct {
		Data struct {
			PublishID string `json:"publish_id"`
		} `json:"data"`
		Error struct {
			Code    string `json:"code"`
			Message string `json:"message"`
		} `json:"error"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	if response.Error.Code != "" {
		return "", fmt.Errorf("TikTok API error: %s - %s", response.Error.Code, response.Error.Message)
	}

	return response.Data.PublishID, nil
}

// GetAnalytics retrieves TikTok analytics
func (ts *TikTokService) GetAnalytics(days int) (*TikTokAnalytics, error) {
	url := fmt.Sprintf("%s/v2/research/adlib/", ts.BaseURL)

	// TikTok analytics endpoint (simplified for demo)
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+ts.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get analytics: %w", err)
	}
	defer resp.Body.Close()

	// For now, return mock data since TikTok analytics API is complex
	analytics := &TikTokAnalytics{
		ProfileViews: 1250,
		VideoViews:   15600,
		Likes:        890,
		Comments:     156,
		Shares:       78,
		Followers:    445,
	}

	return analytics, nil
}

// ValidateToken checks if the TikTok access token is valid
func (ts *TikTokService) ValidateToken() error {
	_, err := ts.GetUserInfo()
	return err
}

// GenerateRecoveryContent creates TikTok-appropriate content from YouTube videos
func (ts *TikTokService) GenerateRecoveryContent(videoTitle, videoURL, accountType string) CreateVideoRequest {
	var title, description string
	var hashtags []string

	switch accountType {
	case "merch":
		title = fmt.Sprintf("Recovery merch drop! 🔥 %s", videoTitle)
		description = fmt.Sprintf("New recovery content + merch available! Link in bio 🙏 %s", videoTitle)
		hashtags = []string{"recovery", "sobriety", "merch", "recoverymerch", "onedayatatime", "aa", "na", "sober"}
	case "memes":
		title = fmt.Sprintf("Recovery vibes 😅 %s", videoTitle)
		description = fmt.Sprintf("When recovery hits different 😂 %s #RecoveryMemes", videoTitle)
		hashtags = []string{"recovery", "sobriety", "memes", "recoverymemes", "sobrietymemes", "aa", "na", "sober", "funny"}
	case "serious":
		title = fmt.Sprintf("Recovery wisdom 🙏 %s", videoTitle)
		description = fmt.Sprintf("Powerful recovery story: %s 🙏 #Recovery #Hope", videoTitle)
		hashtags = []string{"recovery", "sobriety", "hope", "healing", "aa", "na", "sober", "inspiration", "motivation"}
	default:
		title = videoTitle
		description = fmt.Sprintf("Recovery content: %s", videoTitle)
		hashtags = []string{"recovery", "sobriety", "aa", "na", "sober"}
	}

	return CreateVideoRequest{
		VideoURL:    videoURL,
		Title:       title,
		Description: description,
		Hashtags:    hashtags,
		Privacy:     "PUBLIC",
	}
}
