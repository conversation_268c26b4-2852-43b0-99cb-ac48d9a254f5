version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - ENVIRONMENT=development
      - DATABASE_TYPE=postgres
      - DATABASE_URL=******************************************/recovery_dashboard?sslmode=disable
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=development-jwt-secret-change-in-production
    depends_on:
      - postgres
      - redis
    volumes:
      - ./uploads:/tmp/recovery-processing
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=recovery_dashboard
      - POSTGRES_USER=recovery
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # Optional: Database admin interface
  adminer:
    image: adminer
    ports:
      - "8081:8080"
    depends_on:
      - postgres
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
