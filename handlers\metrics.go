package handlers

import (
	"net/http"
	"time"

	"recovery-dashboard/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type MetricsHandler struct {
	db *gorm.DB
}

func NewMetricsHandler(db *gorm.DB) *MetricsHandler {
	return &MetricsHandler{db: db}
}

type DashboardMetrics struct {
	ContentMetrics  ContentMetrics  `json:"content"`
	ShopifyMetrics  ShopifyMetrics  `json:"shopify"`
	SocialMetrics   SocialMetrics   `json:"social"`
	AudienceMetrics AudienceMetrics `json:"audience"`
}

type ContentMetrics struct {
	TotalSpeakers      int64   `json:"total_speakers"`
	ProcessedSpeakers  int64   `json:"processed_speakers"`
	PendingSpeakers    int64   `json:"pending_speakers"`
	TotalVideos        int64   `json:"total_videos"`
	CompletedVideos    int64   `json:"completed_videos"`
	RenderingVideos    int64   `json:"rendering_videos"`
	TotalReadings      int64   `json:"total_readings"`
	ProcessingProgress float64 `json:"processing_progress"`
}

type ShopifyMetrics struct {
	TotalOrders      int64   `json:"total_orders"`
	PendingOrders    int64   `json:"pending_orders"`
	CompletedOrders  int64   `json:"completed_orders"`
	TotalRevenue     float64 `json:"total_revenue"`
	MonthlyRevenue   float64 `json:"monthly_revenue"`
	TotalProducts    int64   `json:"total_products"`
	ActiveProducts   int64   `json:"active_products"`
	LowStockProducts int64   `json:"low_stock_products"`
}

type SocialMetrics struct {
	TotalPosts     int64 `json:"total_posts"`
	DraftPosts     int64 `json:"draft_posts"`
	ScheduledPosts int64 `json:"scheduled_posts"`
	PublishedPosts int64 `json:"published_posts"`
	TotalBrands    int64 `json:"total_brands"`
	PostsThisWeek  int64 `json:"posts_this_week"`
	PostsThisMonth int64 `json:"posts_this_month"`
}

type AudienceMetrics struct {
	YouTubeSubscribers int64   `json:"youtube_subscribers"`
	FacebookFollowers  int64   `json:"facebook_followers"`
	InstagramFollowers int64   `json:"instagram_followers"`
	TotalEngagement    int64   `json:"total_engagement"`
	EngagementRate     float64 `json:"engagement_rate"`
	GrowthRate         float64 `json:"growth_rate"`
}

func (h *MetricsHandler) GetDashboardMetrics(c *gin.Context) {
	metrics := DashboardMetrics{}

	// Get content metrics
	contentMetrics, err := h.getContentMetrics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch content metrics"})
		return
	}
	metrics.ContentMetrics = contentMetrics

	// Get Shopify metrics
	shopifyMetrics, err := h.getShopifyMetrics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch Shopify metrics"})
		return
	}
	metrics.ShopifyMetrics = shopifyMetrics

	// Get social metrics
	socialMetrics, err := h.getSocialMetrics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch social metrics"})
		return
	}
	metrics.SocialMetrics = socialMetrics

	// Get audience metrics (placeholder)
	metrics.AudienceMetrics = h.getAudienceMetrics()

	c.JSON(http.StatusOK, gin.H{"metrics": metrics})
}

func (h *MetricsHandler) GetContentMetrics(c *gin.Context) {
	metrics, err := h.getContentMetrics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch content metrics"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"metrics": metrics})
}

func (h *MetricsHandler) GetShopifyMetrics(c *gin.Context) {
	metrics, err := h.getShopifyMetrics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch Shopify metrics"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"metrics": metrics})
}

func (h *MetricsHandler) GetSocialMetrics(c *gin.Context) {
	metrics, err := h.getSocialMetrics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch social metrics"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"metrics": metrics})
}

func (h *MetricsHandler) GetAudienceMetrics(c *gin.Context) {
	metrics := h.getAudienceMetrics()
	c.JSON(http.StatusOK, gin.H{"metrics": metrics})
}

// Helper methods
func (h *MetricsHandler) getContentMetrics() (ContentMetrics, error) {
	var metrics ContentMetrics

	// Return mock data if database is not available
	if h.db == nil {
		return ContentMetrics{
			TotalSpeakers:      25,
			ProcessedSpeakers:  18,
			PendingSpeakers:    7,
			TotalVideos:        42,
			CompletedVideos:    35,
			RenderingVideos:    3,
			TotalReadings:      365,
			ProcessingProgress: 72.0,
		}, nil
	}

	// Speaker metrics
	if err := h.db.Model(&models.Speaker{}).Count(&metrics.TotalSpeakers).Error; err != nil {
		return metrics, err
	}

	if err := h.db.Model(&models.Speaker{}).Where("status = ?", "processed").Count(&metrics.ProcessedSpeakers).Error; err != nil {
		return metrics, err
	}

	if err := h.db.Model(&models.Speaker{}).Where("status = ?", "pending").Count(&metrics.PendingSpeakers).Error; err != nil {
		return metrics, err
	}

	// Video metrics
	if err := h.db.Model(&models.Video{}).Count(&metrics.TotalVideos).Error; err != nil {
		return metrics, err
	}

	if err := h.db.Model(&models.Video{}).Where("render_state = ?", "completed").Count(&metrics.CompletedVideos).Error; err != nil {
		return metrics, err
	}

	if err := h.db.Model(&models.Video{}).Where("render_state = ?", "rendering").Count(&metrics.RenderingVideos).Error; err != nil {
		return metrics, err
	}

	// Reading metrics
	if err := h.db.Model(&models.Reading{}).Count(&metrics.TotalReadings).Error; err != nil {
		return metrics, err
	}

	// Calculate processing progress
	if metrics.TotalSpeakers > 0 {
		metrics.ProcessingProgress = float64(metrics.ProcessedSpeakers) / float64(metrics.TotalSpeakers) * 100
	}

	return metrics, nil
}

func (h *MetricsHandler) getShopifyMetrics() (ShopifyMetrics, error) {
	var metrics ShopifyMetrics

	// Return mock data if database is not available
	if h.db == nil {
		return ShopifyMetrics{
			TotalOrders:      156,
			PendingOrders:    12,
			CompletedOrders:  144,
			TotalRevenue:     15420.50,
			MonthlyRevenue:   3250.75,
			TotalProducts:    28,
			ActiveProducts:   25,
			LowStockProducts: 3,
		}, nil
	}

	// Order metrics
	if err := h.db.Model(&models.Order{}).Count(&metrics.TotalOrders).Error; err != nil {
		return metrics, err
	}

	if err := h.db.Model(&models.Order{}).Where("status = ?", "pending").Count(&metrics.PendingOrders).Error; err != nil {
		return metrics, err
	}

	if err := h.db.Model(&models.Order{}).Where("status IN ?", []string{"paid", "shipped", "delivered"}).Count(&metrics.CompletedOrders).Error; err != nil {
		return metrics, err
	}

	// Revenue metrics
	var totalRevenue struct {
		Total float64
	}
	if err := h.db.Model(&models.Order{}).Select("COALESCE(SUM(total), 0) as total").Where("status != ?", "cancelled").Scan(&totalRevenue).Error; err != nil {
		return metrics, err
	}
	metrics.TotalRevenue = totalRevenue.Total

	// Monthly revenue
	var monthlyRevenue struct {
		Total float64
	}
	startOfMonth := time.Now().AddDate(0, 0, -time.Now().Day()+1)
	if err := h.db.Model(&models.Order{}).Select("COALESCE(SUM(total), 0) as total").
		Where("status != ? AND created_at >= ?", "cancelled", startOfMonth).
		Scan(&monthlyRevenue).Error; err != nil {
		return metrics, err
	}
	metrics.MonthlyRevenue = monthlyRevenue.Total

	// Product metrics
	if err := h.db.Model(&models.Product{}).Count(&metrics.TotalProducts).Error; err != nil {
		return metrics, err
	}

	if err := h.db.Model(&models.Product{}).Where("status = ?", "active").Count(&metrics.ActiveProducts).Error; err != nil {
		return metrics, err
	}

	if err := h.db.Model(&models.Product{}).Where("inventory < ?", 10).Count(&metrics.LowStockProducts).Error; err != nil {
		return metrics, err
	}

	return metrics, nil
}

func (h *MetricsHandler) getSocialMetrics() (SocialMetrics, error) {
	var metrics SocialMetrics

	// Return mock data if database is not available
	if h.db == nil {
		return SocialMetrics{
			TotalPosts:     89,
			DraftPosts:     15,
			ScheduledPosts: 8,
			PublishedPosts: 66,
			TotalBrands:    3,
			PostsThisWeek:  12,
			PostsThisMonth: 45,
		}, nil
	}

	// Post metrics
	if err := h.db.Model(&models.Post{}).Count(&metrics.TotalPosts).Error; err != nil {
		return metrics, err
	}

	if err := h.db.Model(&models.Post{}).Where("status = ?", "draft").Count(&metrics.DraftPosts).Error; err != nil {
		return metrics, err
	}

	if err := h.db.Model(&models.Post{}).Where("status = ?", "scheduled").Count(&metrics.ScheduledPosts).Error; err != nil {
		return metrics, err
	}

	if err := h.db.Model(&models.Post{}).Where("status = ?", "posted").Count(&metrics.PublishedPosts).Error; err != nil {
		return metrics, err
	}

	// Brand metrics
	if err := h.db.Model(&models.Brand{}).Count(&metrics.TotalBrands).Error; err != nil {
		return metrics, err
	}

	// Time-based metrics
	weekAgo := time.Now().AddDate(0, 0, -7)
	if err := h.db.Model(&models.Post{}).Where("created_at >= ?", weekAgo).Count(&metrics.PostsThisWeek).Error; err != nil {
		return metrics, err
	}

	monthAgo := time.Now().AddDate(0, -1, 0)
	if err := h.db.Model(&models.Post{}).Where("created_at >= ?", monthAgo).Count(&metrics.PostsThisMonth).Error; err != nil {
		return metrics, err
	}

	return metrics, nil
}

func (h *MetricsHandler) getAudienceMetrics() AudienceMetrics {
	// TODO: Implement actual API calls to social platforms
	// This would integrate with YouTube Analytics API, Facebook Graph API, etc.

	return AudienceMetrics{
		YouTubeSubscribers: 1250,
		FacebookFollowers:  890,
		InstagramFollowers: 2100,
		TotalEngagement:    15600,
		EngagementRate:     4.2,
		GrowthRate:         12.5,
	}
}
