package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// SnapchatService handles Snapchat API interactions
type SnapchatService struct {
	AccessToken string
	BaseURL     string
}

// NewSnapchatService creates a new Snapchat service instance
func NewSnapchatService(accessToken string) *SnapchatService {
	return &SnapchatService{
		AccessToken: accessToken,
		BaseURL:     "https://adsapi.snapchat.com",
	}
}

// SnapchatUser represents Snapchat user information
type SnapchatUser struct {
	ID          string `json:"id"`
	Username    string `json:"username"`
	DisplayName string `json:"display_name"`
	BitmojURL   string `json:"bitmoji_url"`
}

// SnapchatStory represents a Snapchat story
type SnapchatStory struct {
	ID        string    `json:"id"`
	MediaURL  string    `json:"media_url"`
	MediaType string    `json:"media_type"` // "IMAGE", "VIDEO"
	Duration  int       `json:"duration"`
	CreatedAt time.Time `json:"created_at"`
}

// SnapchatAnalytics represents Snapchat analytics data
type SnapchatAnalytics struct {
	StoryViews  int `json:"story_views"`
	UniqueViews int `json:"unique_views"`
	Screenshots int `json:"screenshots"`
	Shares      int `json:"shares"`
	TotalReach  int `json:"total_reach"`
}

// SnapchatCreateStoryRequest represents a request to create a Snapchat story
type SnapchatCreateStoryRequest struct {
	MediaURL  string `json:"media_url"`
	MediaType string `json:"media_type"` // "IMAGE", "VIDEO"
	Duration  int    `json:"duration"`   // For videos
	Caption   string `json:"caption"`
}

// GetUserInfo retrieves Snapchat user information
func (ss *SnapchatService) GetUserInfo() (*SnapchatUser, error) {
	url := fmt.Sprintf("%s/v1/me", ss.BaseURL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+ss.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Snapchat API error: %s", string(body))
	}

	var response struct {
		User SnapchatUser `json:"user"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode user info: %w", err)
	}

	return &response.User, nil
}

// CreateStory creates a Snapchat story
func (ss *SnapchatService) CreateStory(req SnapchatCreateStoryRequest) (string, error) {
	url := fmt.Sprintf("%s/v1/stories", ss.BaseURL)

	jsonBody, err := json.Marshal(req)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Authorization", "Bearer "+ss.AccessToken)
	httpReq.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return "", fmt.Errorf("failed to create story: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("Snapchat API error: %s", string(body))
	}

	var response struct {
		Story struct {
			ID string `json:"id"`
		} `json:"story"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	return response.Story.ID, nil
}

// GetAnalytics retrieves Snapchat analytics
func (ss *SnapchatService) GetAnalytics(days int) (*SnapchatAnalytics, error) {
	// Mock analytics for now since Snapchat API is complex
	analytics := &SnapchatAnalytics{
		StoryViews:  2100,
		UniqueViews: 1850,
		Screenshots: 45,
		Shares:      23,
		TotalReach:  1950,
	}

	return analytics, nil
}

// ValidateToken checks if the Snapchat access token is valid
func (ss *SnapchatService) ValidateToken() error {
	_, err := ss.GetUserInfo()
	return err
}

// GenerateRecoveryStory creates Snapchat-appropriate content from YouTube videos
func (ss *SnapchatService) GenerateRecoveryStory(videoTitle, videoURL, accountType string) SnapchatCreateStoryRequest {
	var caption string

	switch accountType {
	case "merch":
		caption = fmt.Sprintf("🔥 New recovery drop! %s - Check our story for merch! 🙏 #Recovery #RecoveryMerch", videoTitle)
	case "memes":
		caption = fmt.Sprintf("Recovery mood 😅 %s #RecoveryVibes #SoberLife", videoTitle)
	case "serious":
		caption = fmt.Sprintf("Recovery wisdom 🙏 %s #Recovery #Hope #Healing", videoTitle)
	default:
		caption = fmt.Sprintf("Recovery content: %s #Recovery #Sobriety", videoTitle)
	}

	return SnapchatCreateStoryRequest{
		MediaURL:  videoURL, // This would be converted to Snapchat-compatible format
		MediaType: "VIDEO",
		Duration:  15, // 15 seconds for Snapchat
		Caption:   caption,
	}
}

// CrossPostYouTubeVideo creates a Snapchat story for a YouTube video
func (ss *SnapchatService) CrossPostYouTubeVideo(videoTitle, videoURL, accountType string) (string, error) {
	storyContent := ss.GenerateRecoveryStory(videoTitle, videoURL, accountType)
	return ss.CreateStory(storyContent)
}
