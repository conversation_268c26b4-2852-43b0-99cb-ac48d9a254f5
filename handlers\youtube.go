package handlers

import (
	"context"
	"net/http"
	"strconv"
	"time"

	"recovery-dashboard/config"
	"recovery-dashboard/models"
	"recovery-dashboard/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type YouTubeHandler struct {
	db      *gorm.DB
	cfg     *config.Config
	youtube *services.YouTubeService
}

func NewYouTubeHandler(db *gorm.DB, cfg *config.Config) *YouTubeHandler {
	youtubeService, err := services.NewYouTubeService(cfg, db)
	if err != nil {
		// Log error but don't fail - service will handle gracefully
		youtubeService = nil
	}

	return &YouTubeHandler{
		db:      db,
		cfg:     cfg,
		youtube: youtubeService,
	}
}

// UploadVideo uploads a processed speaker video to YouTube
func (h *YouTubeHandler) UploadVideo(c *gin.Context) {
	if h.youtube == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "YouTube service not available"})
		return
	}

	var req struct {
		SpeakerID   uint   `json:"speaker_id" binding:"required"`
		VideoPath   string `json:"video_path" binding:"required"`
		Topic       string `json:"topic"`
		Privacy     string `json:"privacy"`
		CategoryID  string `json:"category_id"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get speaker details
	var speaker models.Speaker
	if h.db != nil {
		if err := h.db.First(&speaker, req.SpeakerID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Speaker not found"})
			return
		}
	} else {
		// Mock speaker for testing
		speaker = models.Speaker{
			ID:      req.SpeakerID,
			Name:    "Test Speaker",
			Program: "AA",
		}
	}

	// Set defaults
	if req.Privacy == "" {
		req.Privacy = "public"
	}
	if req.CategoryID == "" {
		req.CategoryID = "22" // People & Blogs
	}

	// Generate video metadata
	title := h.youtube.GenerateVideoTitle(speaker, req.Topic)
	description := h.youtube.GenerateVideoDescription(speaker, req.Topic)
	tags := h.youtube.GetVideoTags(speaker, req.Topic)

	// Create upload request
	uploadReq := services.UploadRequest{
		VideoPath:   req.VideoPath,
		Title:       title,
		Description: description,
		Tags:        tags,
		CategoryID:  req.CategoryID,
		Privacy:     req.Privacy,
		SpeakerID:   req.SpeakerID,
	}

	// Upload video
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()

	video, err := h.youtube.UploadVideo(ctx, uploadReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upload video: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":  "Video uploaded successfully",
		"video_id": video.Id,
		"url":      "https://www.youtube.com/watch?v=" + video.Id,
		"title":    video.Snippet.Title,
	})
}

// GetChannelVideos retrieves videos from the Sound of Recovery channel
func (h *YouTubeHandler) GetChannelVideos(c *gin.Context) {
	if h.youtube == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "YouTube service not available"})
		return
	}

	maxResults := int64(50)
	if max := c.Query("max_results"); max != "" {
		if parsed, err := strconv.ParseInt(max, 10, 64); err == nil {
			maxResults = parsed
		}
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	videos, err := h.youtube.GetChannelVideos(ctx, maxResults)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get channel videos: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"videos": videos,
		"count":  len(videos),
	})
}

// GetVideoAnalytics retrieves analytics for a specific video
func (h *YouTubeHandler) GetVideoAnalytics(c *gin.Context) {
	if h.youtube == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "YouTube service not available"})
		return
	}

	videoID := c.Param("video_id")
	if videoID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Video ID is required"})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	analytics, err := h.youtube.GetVideoAnalytics(ctx, videoID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get video analytics: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"analytics": analytics})
}

// GetChannelAnalytics retrieves overall channel analytics
func (h *YouTubeHandler) GetChannelAnalytics(c *gin.Context) {
	if h.youtube == nil {
		// Return mock data for testing
		mockAnalytics := map[string]interface{}{
			"channel_id":       "UC_MOCK_CHANNEL_ID",
			"channel_title":    "Sound of Recovery",
			"subscriber_count": 1250,
			"video_count":      89,
			"view_count":       45600,
			"description":      "Sharing powerful stories of hope and healing from the recovery community",
		}
		c.JSON(http.StatusOK, gin.H{"analytics": mockAnalytics})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	analytics, err := h.youtube.GetChannelAnalytics(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get channel analytics: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"analytics": analytics})
}

// CreatePlaylist creates a new playlist for organizing content
func (h *YouTubeHandler) CreatePlaylist(c *gin.Context) {
	if h.youtube == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "YouTube service not available"})
		return
	}

	var req struct {
		Title       string `json:"title" binding:"required"`
		Description string `json:"description"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	playlist, err := h.youtube.CreatePlaylist(ctx, req.Title, req.Description)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create playlist: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":     "Playlist created successfully",
		"playlist_id": playlist.Id,
		"title":       playlist.Snippet.Title,
		"url":         "https://www.youtube.com/playlist?list=" + playlist.Id,
	})
}

// AddVideoToPlaylist adds a video to a specific playlist
func (h *YouTubeHandler) AddVideoToPlaylist(c *gin.Context) {
	if h.youtube == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "YouTube service not available"})
		return
	}

	var req struct {
		PlaylistID string `json:"playlist_id" binding:"required"`
		VideoID    string `json:"video_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	err := h.youtube.AddVideoToPlaylist(ctx, req.PlaylistID, req.VideoID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to add video to playlist: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Video added to playlist successfully",
	})
}

// ProcessSpeakerVideo processes a speaker's audio and uploads to YouTube
func (h *YouTubeHandler) ProcessSpeakerVideo(c *gin.Context) {
	speakerID, err := strconv.ParseUint(c.Param("speaker_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid speaker ID"})
		return
	}

	var req struct {
		Topic      string `json:"topic"`
		Privacy    string `json:"privacy"`
		AutoUpload bool   `json:"auto_upload"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get speaker details
	var speaker models.Speaker
	if h.db != nil {
		if err := h.db.First(&speaker, uint(speakerID)).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Speaker not found"})
			return
		}

		// Update speaker status to processing
		h.db.Model(&speaker).Update("status", "processing")
	}

	// TODO: Implement actual video processing pipeline
	// This would involve:
	// 1. Audio transcription
	// 2. Video rendering with After Effects
	// 3. Thumbnail generation
	// 4. Automatic upload if requested

	c.JSON(http.StatusAccepted, gin.H{
		"message":    "Speaker video processing started",
		"speaker_id": speakerID,
		"status":     "processing",
	})
}

// GetUploadStatus checks the status of video uploads
func (h *YouTubeHandler) GetUploadStatus(c *gin.Context) {
	// This would track upload progress and status
	// For now, return mock status
	c.JSON(http.StatusOK, gin.H{
		"uploads": []gin.H{
			{
				"id":       1,
				"speaker":  "John D.",
				"status":   "completed",
				"progress": 100,
				"video_id": "dQw4w9WgXcQ",
			},
			{
				"id":       2,
				"speaker":  "Sarah M.",
				"status":   "uploading",
				"progress": 75,
				"video_id": "",
			},
		},
	})
}
