package handlers

import (
	"net/http"
	"strconv"

	"recovery-dashboard/config"
	"recovery-dashboard/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ShopifyHandler struct {
	db  *gorm.DB
	cfg *config.Config
}

func NewShopifyHandler(db *gorm.DB, cfg *config.Config) *ShopifyHandler {
	return &ShopifyHandler{db: db, cfg: cfg}
}

// Order handlers
func (h *ShopifyHandler) GetOrders(c *gin.Context) {
	var orders []models.Order
	
	query := h.db.Model(&models.Order{})
	
	// Add filters
	if status := c.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}
	
	// Pagination
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "20"))
	offset := (page - 1) * limit
	
	if err := query.Preload("Products").Offset(offset).Limit(limit).Find(&orders).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch orders"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"orders": orders,
		"page":   page,
		"limit":  limit,
	})
}

func (h *ShopifyHandler) GetOrder(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}
	
	var order models.Order
	if err := h.db.Preload("Products").First(&order, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Order not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch order"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"order": order})
}

func (h *ShopifyHandler) SyncOrders(c *gin.Context) {
	// TODO: Implement Shopify API integration to sync orders
	// This would call Shopify Admin API to fetch recent orders
	// and update the local database
	
	c.JSON(http.StatusAccepted, gin.H{
		"message": "Order sync started",
		"status":  "processing",
	})
}

// Product handlers
func (h *ShopifyHandler) GetProducts(c *gin.Context) {
	var products []models.Product
	
	query := h.db.Model(&models.Product{})
	
	// Add filters
	if status := c.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}
	
	// Pagination
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset := (page - 1) * limit
	
	if err := query.Offset(offset).Limit(limit).Find(&products).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch products"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"products": products,
		"page":     page,
		"limit":    limit,
	})
}

func (h *ShopifyHandler) GetProduct(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid product ID"})
		return
	}
	
	var product models.Product
	if err := h.db.First(&product, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Product not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch product"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"product": product})
}

func (h *ShopifyHandler) SyncProducts(c *gin.Context) {
	// TODO: Implement Shopify API integration to sync products
	// This would call Shopify Admin API to fetch products
	// and update the local database
	
	c.JSON(http.StatusAccepted, gin.H{
		"message": "Product sync started",
		"status":  "processing",
	})
}

// Promotion handlers
func (h *ShopifyHandler) CreatePromotion(c *gin.Context) {
	var req struct {
		Title       string  `json:"title" binding:"required"`
		Code        string  `json:"code" binding:"required"`
		Discount    float64 `json:"discount" binding:"required"`
		Type        string  `json:"type" binding:"required"` // percentage, fixed
		ExpiresAt   string  `json:"expires_at"`
		MinAmount   float64 `json:"min_amount"`
		UsageLimit  int     `json:"usage_limit"`
		Description string  `json:"description"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// TODO: Implement Shopify API call to create discount code
	// This would use Shopify Admin API to create a new discount
	
	c.JSON(http.StatusCreated, gin.H{
		"message":   "Promotion created successfully",
		"promotion": req,
	})
}

func (h *ShopifyHandler) GetPromotions(c *gin.Context) {
	// TODO: Implement Shopify API integration to fetch promotions
	// This would call Shopify Admin API to get discount codes
	
	c.JSON(http.StatusOK, gin.H{
		"promotions": []gin.H{},
		"message":    "Promotions endpoint - to be implemented",
	})
}
