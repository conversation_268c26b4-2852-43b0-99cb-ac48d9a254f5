package handlers

import (
	"net/http"
	"strconv"

	"recovery-dashboard/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ContentHandler struct {
	db *gorm.DB
}

func NewContentHandler(db *gorm.DB) *ContentHandler {
	return &ContentHandler{db: db}
}

// Speaker handlers
func (h *ContentHandler) GetSpeakers(c *gin.Context) {
	// Return mock data if database is not available
	if h.db == nil {
		mockSpeakers := []models.Speaker{
			{ID: 1, Name: "John D.", Program: "AA", Status: "processed"},
			{ID: 2, Name: "<PERSON>", Program: "NA", Status: "pending"},
			{ID: 3, Name: "<PERSON>", Program: "AA", Status: "processed"},
		}
		c.<PERSON>(http.StatusOK, gin.H{"speakers": mockSpeakers})
		return
	}

	var speakers []models.Speaker

	query := h.db.Model(&models.Speaker{})

	// Add filters
	if status := c.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}
	if program := c.Query("program"); program != "" {
		query = query.Where("program = ?", program)
	}

	if err := query.Preload("Videos").Find(&speakers).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch speakers"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"speakers": speakers})
}

func (h *ContentHandler) CreateSpeaker(c *gin.Context) {
	var speaker models.Speaker

	if err := c.ShouldBindJSON(&speaker); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.db.Create(&speaker).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create speaker"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"speaker": speaker})
}

func (h *ContentHandler) GetSpeaker(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid speaker ID"})
		return
	}

	var speaker models.Speaker
	if err := h.db.Preload("Videos").First(&speaker, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Speaker not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch speaker"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"speaker": speaker})
}

func (h *ContentHandler) UpdateSpeaker(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid speaker ID"})
		return
	}

	var speaker models.Speaker
	if err := h.db.First(&speaker, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Speaker not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch speaker"})
		return
	}

	var updateData models.Speaker
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.db.Model(&speaker).Updates(updateData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update speaker"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"speaker": speaker})
}

func (h *ContentHandler) DeleteSpeaker(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid speaker ID"})
		return
	}

	if err := h.db.Delete(&models.Speaker{}, uint(id)).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete speaker"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Speaker deleted successfully"})
}

// Video handlers
func (h *ContentHandler) GetVideos(c *gin.Context) {
	var videos []models.Video

	query := h.db.Model(&models.Video{})

	if status := c.Query("render_state"); status != "" {
		query = query.Where("render_state = ?", status)
	}

	if err := query.Preload("Speaker").Find(&videos).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch videos"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"videos": videos})
}

func (h *ContentHandler) CreateVideo(c *gin.Context) {
	var video models.Video

	if err := c.ShouldBindJSON(&video); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.db.Create(&video).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create video"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"video": video})
}

func (h *ContentHandler) GetVideo(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid video ID"})
		return
	}

	var video models.Video
	if err := h.db.Preload("Speaker").First(&video, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Video not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch video"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"video": video})
}

func (h *ContentHandler) UpdateVideo(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid video ID"})
		return
	}

	var video models.Video
	if err := h.db.First(&video, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Video not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch video"})
		return
	}

	var updateData models.Video
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.db.Model(&video).Updates(updateData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update video"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"video": video})
}

func (h *ContentHandler) DeleteVideo(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid video ID"})
		return
	}

	if err := h.db.Delete(&models.Video{}, uint(id)).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete video"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Video deleted successfully"})
}

// Pipeline handlers
func (h *ContentHandler) UpdatePipelineStatus(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var req struct {
		Status string `json:"status" binding:"required"`
		Type   string `json:"type" binding:"required"` // speaker or video
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	switch req.Type {
	case "speaker":
		if err := h.db.Model(&models.Speaker{}).Where("id = ?", uint(id)).Update("status", req.Status).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update speaker status"})
			return
		}
	case "video":
		if err := h.db.Model(&models.Video{}).Where("id = ?", uint(id)).Update("render_state", req.Status).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update video status"})
			return
		}
	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid type"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Status updated successfully"})
}

func (h *ContentHandler) ProcessContent(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	// TODO: Implement content processing logic
	// This would trigger background jobs for transcription, rendering, etc.

	c.JSON(http.StatusAccepted, gin.H{
		"message": "Content processing started",
		"id":      uint(id),
	})
}

// Reading handlers (placeholder implementations)
func (h *ContentHandler) GetReadings(c *gin.Context) {
	var readings []models.Reading
	if err := h.db.Preload("Book").Find(&readings).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch readings"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"readings": readings})
}

func (h *ContentHandler) CreateReading(c *gin.Context) {
	var reading models.Reading
	if err := c.ShouldBindJSON(&reading); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.db.Create(&reading).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create reading"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"reading": reading})
}

func (h *ContentHandler) GetReading(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid reading ID"})
		return
	}

	var reading models.Reading
	if err := h.db.Preload("Book").First(&reading, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Reading not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch reading"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"reading": reading})
}

func (h *ContentHandler) UpdateReading(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid reading ID"})
		return
	}

	var reading models.Reading
	if err := h.db.First(&reading, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Reading not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch reading"})
		return
	}

	var updateData models.Reading
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.db.Model(&reading).Updates(updateData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update reading"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"reading": reading})
}

func (h *ContentHandler) GetTodayReading(c *gin.Context) {
	// TODO: Implement logic to get today's reading
	c.JSON(http.StatusOK, gin.H{"message": "Today's reading endpoint - to be implemented"})
}
