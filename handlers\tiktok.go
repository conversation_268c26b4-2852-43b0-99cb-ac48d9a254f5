package handlers

import (
	"net/http"
	"strconv"

	"recovery-dashboard/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// TikTokHandler handles TikTok-related HTTP requests
type TikTokHandler struct {
	db            *gorm.DB
	tiktokService *services.TikTokService
}

// NewTikTokHandler creates a new TikTok handler
func NewTikTokHandler(db *gorm.DB, tiktokService *services.TikTokService) *TikTokHandler {
	return &TikTokHandler{
		db:            db,
		tiktokService: tiktokService,
	}
}

// GetUserInfo retrieves TikTok user information
func (h *TikTokHandler) GetUserInfo(c *gin.Context) {
	userInfo, err := h.tiktokService.GetUserInfo()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user info: " + err.<PERSON>rror()})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{"user": userInfo})
}

// GetUserVideos retrieves user's TikTok videos
func (h *TikTokHandler) GetUserVideos(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	videos, err := h.tiktokService.GetUserVideos(limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get videos: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"videos": videos,
		"count":  len(videos),
	})
}

// UploadVideo uploads a video to TikTok
func (h *TikTokHandler) UploadVideo(c *gin.Context) {
	var req struct {
		VideoURL    string   `json:"video_url" binding:"required"`
		Title       string   `json:"title" binding:"required"`
		Description string   `json:"description"`
		Hashtags    []string `json:"hashtags"`
		Privacy     string   `json:"privacy_level"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default privacy if not specified
	if req.Privacy == "" {
		req.Privacy = "PUBLIC"
	}

	uploadReq := services.CreateVideoRequest{
		VideoURL:    req.VideoURL,
		Title:       req.Title,
		Description: req.Description,
		Hashtags:    req.Hashtags,
		Privacy:     req.Privacy,
	}

	publishID, err := h.tiktokService.UploadVideo(uploadReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upload video: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"publish_id": publishID,
		"message":    "Video uploaded successfully",
	})
}

// GetAnalytics retrieves TikTok analytics
func (h *TikTokHandler) GetAnalytics(c *gin.Context) {
	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 || days > 365 {
		days = 30
	}

	analytics, err := h.tiktokService.GetAnalytics(days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get analytics: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"analytics": analytics})
}

// ValidateToken checks if the TikTok access token is valid
func (h *TikTokHandler) ValidateToken(c *gin.Context) {
	err := h.tiktokService.ValidateToken()
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"valid": false,
			"error": "Invalid TikTok token: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"valid":   true,
		"message": "TikTok token is valid",
	})
}

// CrossPostYouTubeVideo creates a TikTok post for a YouTube video
func (h *TikTokHandler) CrossPostYouTubeVideo(c *gin.Context) {
	var req struct {
		VideoID     string `json:"video_id" binding:"required"`
		VideoTitle  string `json:"video_title" binding:"required"`
		VideoURL    string `json:"video_url" binding:"required"`
		AccountType string `json:"account_type"` // "merch", "memes", "serious"
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default account type
	if req.AccountType == "" {
		req.AccountType = "serious"
	}

	// Generate TikTok-appropriate content
	tiktokContent := h.tiktokService.GenerateRecoveryContent(req.VideoTitle, req.VideoURL, req.AccountType)

	publishID, err := h.tiktokService.UploadVideo(tiktokContent)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cross-post video: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":            true,
		"publish_id":         publishID,
		"video_id":           req.VideoID,
		"tiktok_publish_id":  publishID,
		"message":            "YouTube video cross-posted to TikTok successfully",
		"generated_content": gin.H{
			"title":       tiktokContent.Title,
			"description": tiktokContent.Description,
			"hashtags":    tiktokContent.Hashtags,
		},
	})
}

// GetAccountAnalytics provides comprehensive analytics for TikTok account
func (h *TikTokHandler) GetAccountAnalytics(c *gin.Context) {
	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 || days > 365 {
		days = 30
	}

	// Get user info and analytics
	userInfo, err := h.tiktokService.GetUserInfo()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user info: " + err.Error()})
		return
	}

	analytics, err := h.tiktokService.GetAnalytics(days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get analytics: " + err.Error()})
		return
	}

	// Get recent videos for engagement analysis
	videos, err := h.tiktokService.GetUserVideos(10)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recent videos: " + err.Error()})
		return
	}

	// Calculate engagement metrics
	totalViews := 0
	totalLikes := 0
	totalComments := 0
	totalShares := 0
	for _, video := range videos {
		totalViews += video.ViewCount
		totalLikes += video.LikeCount
		totalComments += video.CommentCount
		totalShares += video.ShareCount
	}

	engagementRate := 0.0
	if totalViews > 0 {
		engagementRate = float64(totalLikes+totalComments+totalShares) / float64(totalViews) * 100
	}

	accountAnalytics := gin.H{
		"user_info": userInfo,
		"analytics": analytics,
		"engagement": gin.H{
			"total_views":     totalViews,
			"total_likes":     totalLikes,
			"total_comments":  totalComments,
			"total_shares":    totalShares,
			"engagement_rate": engagementRate,
			"recent_videos":   len(videos),
		},
		"recent_videos": videos,
		"period_days":   days,
	}

	c.JSON(http.StatusOK, gin.H{"analytics": accountAnalytics})
}

// GenerateContentPreview generates a preview of how YouTube content would look on TikTok
func (h *TikTokHandler) GenerateContentPreview(c *gin.Context) {
	var req struct {
		VideoTitle  string `json:"video_title" binding:"required"`
		VideoURL    string `json:"video_url" binding:"required"`
		AccountType string `json:"account_type"` // "merch", "memes", "serious"
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default account type
	if req.AccountType == "" {
		req.AccountType = "serious"
	}

	// Generate TikTok-appropriate content preview
	content := h.tiktokService.GenerateRecoveryContent(req.VideoTitle, req.VideoURL, req.AccountType)

	c.JSON(http.StatusOK, gin.H{
		"preview": gin.H{
			"title":       content.Title,
			"description": content.Description,
			"hashtags":    content.Hashtags,
			"privacy":     content.Privacy,
		},
		"account_type": req.AccountType,
		"message":      "Content preview generated successfully",
	})
}
