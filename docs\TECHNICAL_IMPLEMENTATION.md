# Recovery Dashboard - Technical Implementation Guide

## 🏗️ System Architecture

### **Technology Stack**
- **Backend**: Go 1.21+ with Gin framework
- **Database**: PostgreSQL with GORM ORM
- **Frontend**: HTML/CSS/JavaScript with Tailwind CSS
- **APIs**: RESTful API design
- **Authentication**: JWT-based auth system

### **Project Structure**
```
recovery-dashboard/
├── cmd/                    # Application entry points
├── config/                 # Configuration management
├── handlers/               # HTTP request handlers
├── models/                 # Database models
├── routes/                 # API route definitions
├── services/               # Business logic and external API integrations
├── templates/              # HTML templates
├── static/                 # Static assets (CSS, JS, images)
├── docs/                   # Documentation
└── main.go                 # Application entry point
```

## 🔌 Platform Integration Architecture

### **Service Layer Design**
Each social media platform has its own service with standardized interfaces:

```go
// Common interface pattern
type SocialMediaService interface {
    ValidateToken() error
    GetUserInfo() (*UserInfo, error)
    CreatePost(content PostContent) (string, error)
    GetAnalytics(days int) (*Analytics, error)
}
```

### **Multi-Platform Handler**
Central handler coordinates cross-platform operations:

```go
type MultiPlatformSocialHandler struct {
    snapchatService *services.SnapchatService
    threadsService  *services.ThreadsService
    xService        *services.XService
}
```

## 📱 Platform-Specific Implementations

### **Facebook Multi-Page System**
```go
// Multi-page Facebook service
type MultiPageFacebookService struct {
    Pages map[string]*FacebookPage
}

type FacebookPage struct {
    ID          string
    Name        string
    Token       string
    BrandType   string // "serious", "humor", "spiritual"
    Description string
}
```

**Key Features:**
- Individual page token management
- Brand-specific content generation
- Cross-page analytics aggregation
- Unified posting interface

### **TikTok Multi-Account System**
```go
// TikTok service with account type awareness
func (ts *TikTokService) GenerateRecoveryContent(videoTitle, videoURL, accountType string) CreateVideoRequest {
    switch accountType {
    case "merch":
        // Merchandise-focused content
    case "memes":
        // Humor-based content
    case "serious":
        // Inspirational content
    }
}
```

**Content Adaptation:**
- Account-specific messaging
- Hashtag optimization per account type
- Platform-specific formatting (15-second videos)

### **Cross-Platform Content Generation**
```go
// Smart content adaptation
func (h *MultiPlatformSocialHandler) GenerateContentPreview(videoTitle, videoURL, accountType string) {
    // Snapchat: 15-second video story
    snapPreview := h.snapchatService.GenerateRecoveryStory(videoTitle, videoURL, accountType)
    
    // Threads: Long-form text content
    threadsPreview := h.threadsService.GenerateRecoveryPost(videoTitle, videoURL, accountType)
    
    // X: 280-character optimized tweet
    xPreview := h.xService.GenerateRecoveryTweet(videoTitle, videoURL, accountType)
}
```

## 🔄 Cross-Posting Workflow

### **1. Content Input**
```json
{
    "video_id": "abc123",
    "video_title": "John D. - Step 4 Recovery Story",
    "video_url": "https://youtube.com/watch?v=abc123",
    "account_type": "merch"
}
```

### **2. Platform-Specific Content Generation**
```go
// Each platform gets optimized content
func (h *MultiPlatformSocialHandler) CrossPostToAllPlatforms(req CrossPostRequest) {
    // Parallel processing for all platforms
    var wg sync.WaitGroup
    results := make(map[string]interface{})
    
    // Snapchat
    wg.Add(1)
    go func() {
        defer wg.Done()
        storyID, err := h.snapchatService.CrossPostYouTubeVideo(req.VideoTitle, req.VideoURL, req.AccountType)
        results["snapchat"] = processResult(storyID, err)
    }()
    
    // Threads
    wg.Add(1)
    go func() {
        defer wg.Done()
        postID, err := h.threadsService.CrossPostYouTubeVideo(req.VideoTitle, req.VideoURL, req.AccountType)
        results["threads"] = processResult(postID, err)
    }()
    
    // X (Twitter)
    wg.Add(1)
    go func() {
        defer wg.Done()
        tweetID, err := h.xService.CrossPostYouTubeVideo(req.VideoTitle, req.VideoURL, req.AccountType)
        results["x"] = processResult(tweetID, err)
    }()
    
    wg.Wait()
    return results
}
```

### **3. Simultaneous Platform Posting**
- **Goroutines**: Parallel execution for speed
- **Error Handling**: Individual platform failures don't affect others
- **Result Aggregation**: Detailed success/failure reporting per platform

## 🎯 Content Adaptation Examples

### **Brand-Specific Messaging**
```go
func generateBrandMessage(brandType, videoTitle string) string {
    switch brandType {
    case "merch":
        return fmt.Sprintf("🔥 New recovery content + merch drop!\n\n%s\n\nRecovery gear that matters 🙏", videoTitle)
    case "memes":
        return fmt.Sprintf("Recovery mood 😅\n\n%s\n\nWhen the program hits different 💯", videoTitle)
    case "serious":
        return fmt.Sprintf("Powerful recovery wisdom 🙏\n\n%s\n\nEvery story matters. Keep going.", videoTitle)
    }
}
```

### **Platform-Specific Formatting**
```go
// X (Twitter) character limit handling
func (xs *XService) GenerateRecoveryTweet(videoTitle, videoURL, accountType string) XCreateTweetRequest {
    text := generateBrandMessage(accountType, videoTitle) + "\n\n" + videoURL
    
    // Ensure tweet is under 280 characters
    if len(text) > 280 {
        text = text[:277] + "..."
    }
    
    return XCreateTweetRequest{Text: text}
}
```

## 📊 Dashboard Implementation

### **Frontend Architecture**
```javascript
// Real-time platform status loading
async function loadAdditionalPlatforms() {
    const response = await fetch('/api/v1/social-multi/platforms/status');
    const data = await response.json();
    
    // Dynamic UI generation based on platform status
    renderPlatformCards(data.platforms);
}

// Cross-platform content preview
async function previewMultiPlatformContent() {
    const response = await fetch('/api/v1/social-multi/content-preview', {
        method: 'POST',
        body: JSON.stringify(previewData)
    });
    
    // Show platform-specific previews
    displayContentPreviews(response.previews);
}
```

### **Interactive Features**
- **Real-time Status**: Live platform connection indicators
- **Content Preview**: Platform-specific content generation preview
- **One-Click Cross-Post**: Unified posting interface
- **Error Handling**: User-friendly error messages

## 🔐 Security & Authentication

### **API Token Management**
```go
// Secure token storage in environment variables
type Config struct {
    // Facebook Multi-Page
    FacebookSoundOfRecoveryToken string
    FacebookRecoveryMemesToken   string
    
    // TikTok Multi-Account
    TikTokRecoveryMerchToken string
    TikTokRecoveryMemesToken string
    TikTokSeriousToken       string
    
    // Additional Platforms
    SnapchatToken string
    ThreadsToken  string
    XToken        string
}
```

### **Token Validation**
```go
// Real-time token validation
func (h *MultiPlatformSocialHandler) GetAllPlatformsStatus() map[string]interface{} {
    status := make(map[string]interface{})
    
    // Test each platform token
    for platform, service := range h.services {
        err := service.ValidateToken()
        status[platform] = map[string]interface{}{
            "configured":  service != nil,
            "token_valid": err == nil,
            "error":       getErrorMessage(err),
        }
    }
    
    return status
}
```

## 🚀 Deployment & Scaling

### **Environment Configuration**
```env
# Production environment variables
PORT=8081
DATABASE_URL=postgresql://user:pass@localhost/recovery_db

# Platform API tokens (production)
YOUTUBE_API_KEY=prod-youtube-key
FACEBOOK_SOUND_OF_RECOVERY_TOKEN=prod-fb-token-1
TIKTOK_RECOVERY_MERCH_TOKEN=prod-tiktok-token-1
# ... additional tokens
```

### **Scalability Considerations**
- **Microservice Architecture**: Each platform service can be scaled independently
- **Database Optimization**: Indexed queries for analytics and content retrieval
- **Caching Strategy**: Redis for frequently accessed data
- **Rate Limiting**: Platform-specific API rate limit handling

## 📈 Analytics & Monitoring

### **Cross-Platform Analytics**
```go
func (h *MultiPlatformSocialHandler) GetPlatformAnalytics(days int) map[string]interface{} {
    analytics := make(map[string]interface{})
    
    // Aggregate analytics from all platforms
    for platform, service := range h.services {
        platformAnalytics, err := service.GetAnalytics(days)
        if err == nil {
            analytics[platform] = platformAnalytics
        }
    }
    
    return analytics
}
```

### **Performance Monitoring**
- **API Response Times**: Track platform API performance
- **Success Rates**: Monitor cross-posting success rates
- **Error Tracking**: Detailed error logging per platform
- **Usage Analytics**: Track dashboard feature usage

This technical implementation provides a robust, scalable foundation for managing a complete social media ecosystem with platform-specific optimizations and unified management capabilities.
